package com.rbts.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StudentBookingRequest {
    
    @NotNull
    private Long sessionId;
    
    @NotNull
    private Long studentId;
    
    private Long creditPointsUsed;
    
    private String notes;
}
