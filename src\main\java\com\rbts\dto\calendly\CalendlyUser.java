package com.rbts.dto.calendly;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalendlyUser {
    
    private String uri;
    private String name;
    private String slug;
    private String email;
    
    @JsonProperty("scheduling_url")
    private String schedulingUrl;
    
    private String timezone;
    
    @JsonProperty("avatar_url")
    private String avatarUrl;
    
    @JsonProperty("created_at")
    private OffsetDateTime createdAt;
    
    @JsonProperty("updated_at")
    private OffsetDateTime updatedAt;
    
    @JsonProperty("current_organization")
    private String currentOrganization;
    
    @JsonProperty("resource_type")
    private String resourceType;
}
