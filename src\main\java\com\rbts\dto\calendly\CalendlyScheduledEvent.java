package com.rbts.dto.calendly;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalendlyScheduledEvent {
    
    private String uri;
    private String name;
    private String status;
    
    @JsonProperty("start_time")
    private OffsetDateTime startTime;
    
    @JsonProperty("end_time")
    private OffsetDateTime endTime;
    
    @JsonProperty("event_type")
    private String eventType;
    
    private String location;
    
    @JsonProperty("invitees_counter")
    private InviteesCounter inviteesCounter;
    
    @JsonProperty("created_at")
    private OffsetDateTime createdAt;
    
    @JsonProperty("updated_at")
    private OffsetDateTime updatedAt;
    
    @JsonProperty("event_memberships")
    private List<EventMembership> eventMemberships;
    
    @JsonProperty("event_guests")
    private List<EventGuest> eventGuests;
    
    @JsonProperty("calendar_event")
    private CalendarEvent calendarEvent;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InviteesCounter {
        private Integer total;
        private Integer active;
        private Integer limit;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EventMembership {
        private String user;
        
        @JsonProperty("user_email")
        private String userEmail;
        
        @JsonProperty("user_name")
        private String userName;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EventGuest {
        private String email;
        
        @JsonProperty("created_at")
        private OffsetDateTime createdAt;
        
        @JsonProperty("updated_at")
        private OffsetDateTime updatedAt;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CalendarEvent {
        private String kind;
        
        @JsonProperty("external_id")
        private String externalId;
    }
}
