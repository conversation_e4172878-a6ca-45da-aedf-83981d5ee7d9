package com.rbts.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StudentBookingResponse {
    
    private Long bookingId;
    private Long sessionId;
    private String sessionTitle;
    private String sessionDescription;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime sessionStartTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime sessionEndTime;
    
    private Long tutorId;
    private String tutorName;
    private String tutorEmail;
    
    private Long studentId;
    private String studentName;
    private String studentEmail;
    
    private String subjectName;
    private String sessionTypeName;
    private String sessionLocationName;
    
    private BigDecimal sessionPrice;
    private String currencyCode;
    private Long creditPointsUsed;
    
    private String bookingStatus;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime bookedAt;
    
    // Calendly integration fields
    private String eventTypeUri;
    private String schedulingUrl;
    private String calendlyEventUri;
}
