package com.rbts.service.auth;

import com.rbts.dto.SignupRequest;
import com.rbts.dto.calendly.request.CreateOrganizationInvitationRequest;
import com.rbts.entity.ContactDetails;
import com.rbts.entity.OrganizationInvite;
import com.rbts.entity.StatusMaster;
import com.rbts.entity.auth.Users;
import com.rbts.repository.OrganizationInviteRepository;
import com.rbts.service.calendly.CalendlyService;
import com.rbts.service.common.DynamicEntityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class EnhancedRegistrationService {

    private final SignupService signupService;
    private final CalendlyService calendlyService;
    private final OrganizationInviteRepository organizationInviteRepository;
    private final DynamicEntityService dynamicEntityService;

    /**
     * Enhanced registration that includes Calendly organization invite
     */
    @Transactional
    public Mono<ResponseEntity<Map<String, Object>>> registerUserWithCalendlyInvite(SignupRequest signupRequest) {
        log.info("Starting enhanced registration for user: {}", signupRequest.getUsername());
        
        return Mono.fromCallable(() -> {
            // Step 1: Create user account with PENDING status
            signupRequest.setStatus("PENDING"); // User starts as PENDING until Calendly invite is accepted
            ResponseEntity<Long> userResponse = signupService.save(signupRequest);
            
            if (userResponse.getStatusCode().is2xxSuccessful()) {
                Long userId = userResponse.getBody();
                log.info("User created with ID: {}, now sending Calendly invite", userId);
                
                // Get the created contact details
                ContactDetails contact = getContactDetailsByUserId(userId);
                
                return Map.of(
                    "userId", userId,
                    "contact", contact,
                    "email", signupRequest.getUsername()
                );
            } else {
                throw new RuntimeException("Failed to create user account");
            }
        })
        .flatMap(userData -> {
            Long userId = (Long) userData.get("userId");
            ContactDetails contact = (ContactDetails) userData.get("contact");
            String email = (String) userData.get("email");
            
            // Step 2: Send Calendly organization invitation
            CreateOrganizationInvitationRequest calendlyRequest = CreateOrganizationInvitationRequest.builder()
                    .email(email)
                    .build();
            
            return calendlyService.createOrganizationInvitation(calendlyRequest)
                    .map(calendlyInvitation -> {
                        // Step 3: Create local organization invite record
                        StatusMaster statusMaster = getStatusMasterByName("ACTIVE");
                        
                        OrganizationInvite orgInvite = OrganizationInvite.builder()
                                .contact(contact)
                                .email(email)
                                .calendlyInvitationUri(calendlyInvitation.getUri())
                                .status("PENDING")
                                .invitedAt(LocalDateTime.now())
                                .lastSentAt(calendlyInvitation.getLastSentAt() != null ? 
                                          calendlyInvitation.getLastSentAt().toLocalDateTime() : 
                                          LocalDateTime.now())
                                .hasCalendarPermission(false)
                                .statusMaster(statusMaster)
                                .notes("Registration invite - user must accept to complete registration")
                                .build();
                        
                        OrganizationInvite savedInvite = organizationInviteRepository.save(orgInvite);
                        
                        Map<String, Object> response = new HashMap<>();
                        response.put("userId", userId);
                        response.put("inviteId", savedInvite.getId());
                        response.put("email", email);
                        response.put("status", "PENDING");
                        response.put("message", "User registered successfully. Please check your email for Calendly organization invitation to complete registration.");
                        response.put("calendlyInvitationUri", calendlyInvitation.getUri());
                        response.put("registrationComplete", false);
                        
                        return response;
                    });
        })
        .map(response -> ResponseEntity.ok(response))
        .onErrorResume(error -> {
            log.error("Failed to complete enhanced registration", error);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Registration failed: " + error.getMessage());
            errorResponse.put("registrationComplete", false);
            return Mono.just(ResponseEntity.status(500).body(errorResponse));
        });
    }

    /**
     * Check and update registration status based on Calendly invite acceptance
     */
    @Transactional
    public Mono<ResponseEntity<Map<String, Object>>> checkRegistrationStatus(Long userId) {
        log.info("Checking registration status for user: {}", userId);
        
        return Mono.fromCallable(() -> {
            ContactDetails contact = getContactDetailsByUserId(userId);
            Optional<OrganizationInvite> inviteOpt = organizationInviteRepository.findByContactAndStatus(contact, "PENDING");
            
            if (inviteOpt.isEmpty()) {
                // Check if already accepted
                Optional<OrganizationInvite> acceptedInvite = organizationInviteRepository.findAcceptedInviteWithCalendarPermission(userId);
                if (acceptedInvite.isPresent()) {
                    Map<String, Object> response = new HashMap<>();
                    response.put("userId", userId);
                    response.put("status", "COMPLETED");
                    response.put("registrationComplete", true);
                    response.put("message", "Registration already completed");
                    return response;
                } else {
                    throw new RuntimeException("No organization invitation found for user");
                }
            }
            
            return inviteOpt.get();
        })
        .flatMap(invite -> {
            if (invite.getCalendlyInvitationUri() != null) {
                return calendlyService.getOrganizationInvitation(invite.getCalendlyInvitationUri())
                        .map(calendlyInvitation -> {
                            Map<String, Object> response = new HashMap<>();
                            response.put("userId", userId);
                            response.put("inviteId", invite.getId());
                            response.put("email", invite.getEmail());
                            response.put("status", calendlyInvitation.getStatus());
                            response.put("invitedAt", invite.getInvitedAt());
                            
                            // Update local status if Calendly status changed
                            if ("ACCEPTED".equals(calendlyInvitation.getStatus()) && !"ACCEPTED".equals(invite.getStatus())) {
                                invite.setStatus("ACCEPTED");
                                invite.setAcceptedAt(LocalDateTime.now());
                                invite.setCalendlyUserUri(calendlyInvitation.getUser());
                                invite.setHasCalendarPermission(true); // Assume calendar permission granted on acceptance
                                organizationInviteRepository.save(invite);
                                
                                // Complete user registration by updating status to ACTIVE
                                completeUserRegistration(userId);
                                
                                response.put("registrationComplete", true);
                                response.put("message", "Registration completed successfully! You now have access to the platform.");
                                response.put("acceptedAt", invite.getAcceptedAt());
                                response.put("calendlyUserUri", invite.getCalendlyUserUri());
                            } else {
                                response.put("registrationComplete", false);
                                response.put("message", "Registration pending - please accept the Calendly organization invitation to complete registration.");
                            }
                            
                            response.put("hasCalendarPermission", invite.getHasCalendarPermission());
                            return response;
                        });
            } else {
                Map<String, Object> response = new HashMap<>();
                response.put("userId", userId);
                response.put("status", invite.getStatus());
                response.put("registrationComplete", false);
                response.put("message", "No Calendly invitation URI found");
                return Mono.just(response);
            }
        })
        .map(response -> ResponseEntity.ok(response))
        .onErrorResume(error -> {
            log.error("Failed to check registration status for user: {}", userId, error);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", error.getMessage());
            errorResponse.put("registrationComplete", false);
            return Mono.just(ResponseEntity.status(404).body(errorResponse));
        });
    }

    /**
     * Complete user registration by updating user status to ACTIVE
     */
    @Transactional
    public void completeUserRegistration(Long userId) {
        try {
            signupService.updateStatus(userId, "ACTIVE");
            log.info("User registration completed for user ID: {}", userId);
        } catch (Exception e) {
            log.error("Failed to complete user registration for user ID: {}", userId, e);
            throw new RuntimeException("Failed to complete user registration", e);
        }
    }

    /**
     * Resend Calendly organization invitation
     */
    public Mono<ResponseEntity<Map<String, Object>>> resendCalendlyInvite(Long userId) {
        log.info("Resending Calendly invite for user: {}", userId);
        
        return Mono.fromCallable(() -> {
            ContactDetails contact = getContactDetailsByUserId(userId);
            Optional<OrganizationInvite> inviteOpt = organizationInviteRepository.findByContactAndStatus(contact, "PENDING");
            
            if (inviteOpt.isEmpty()) {
                throw new RuntimeException("No pending organization invitation found for user");
            }
            
            return inviteOpt.get();
        })
        .flatMap(invite -> {
            // Create new Calendly invitation
            CreateOrganizationInvitationRequest calendlyRequest = CreateOrganizationInvitationRequest.builder()
                    .email(invite.getEmail())
                    .build();
            
            return calendlyService.createOrganizationInvitation(calendlyRequest)
                    .map(calendlyInvitation -> {
                        // Update existing invite record
                        invite.setCalendlyInvitationUri(calendlyInvitation.getUri());
                        invite.setLastSentAt(LocalDateTime.now());
                        organizationInviteRepository.save(invite);
                        
                        Map<String, Object> response = new HashMap<>();
                        response.put("userId", userId);
                        response.put("inviteId", invite.getId());
                        response.put("email", invite.getEmail());
                        response.put("message", "Calendly invitation resent successfully");
                        response.put("lastSentAt", invite.getLastSentAt());
                        
                        return response;
                    });
        })
        .map(response -> ResponseEntity.ok(response))
        .onErrorResume(error -> {
            log.error("Failed to resend Calendly invite for user: {}", userId, error);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", error.getMessage());
            return Mono.just(ResponseEntity.status(500).body(errorResponse));
        });
    }

    // Helper methods
    private ContactDetails getContactDetailsByUserId(Long userId) {
        try {
            List<Map<String, Object>> data = dynamicEntityService.getEntitiesByField("ContactDetails", "userId", userId.toString());
            if (data.isEmpty()) {
                throw new RuntimeException("ContactDetails not found for user ID: " + userId);
            }
            
            Map<String, Object> contactData = data.get(0);
            return ContactDetails.builder()
                    .id(Long.valueOf(contactData.get("id").toString()))
                    .emailId(contactData.get("emailId").toString())
                    .displayName(contactData.get("displayName").toString())
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to get ContactDetails for user ID: " + userId, e);
        }
    }

    private StatusMaster getStatusMasterByName(String statusName) {
        try {
            List<Map<String, Object>> data = dynamicEntityService.getEntitiesByField("StatusMaster", "statusName", statusName);
            if (data.isEmpty()) {
                throw new RuntimeException("StatusMaster not found with name: " + statusName);
            }
            return StatusMaster.builder()
                    .id(Long.valueOf(data.get(0).get("id").toString()))
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to get StatusMaster with name: " + statusName, e);
        }
    }
}
