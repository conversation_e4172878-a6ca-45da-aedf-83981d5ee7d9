package com.rbts.dto.calendly.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateEventTypeRequest {
    
    private String name;
    private Integer duration;
    private String kind;
    
    @JsonProperty("pooling_type")
    private String poolingType;
    
    private String type;
    private String color;
    
    @JsonProperty("internal_note")
    private String internalNote;
    
    @JsonProperty("description_plain")
    private String descriptionPlain;
    
    @JsonProperty("description_html")
    private String descriptionHtml;
    
    private Profile profile;
    
    @JsonProperty("booking_method")
    private String bookingMethod;
    
    @JsonProperty("custom_questions")
    private List<CustomQuestion> customQuestions;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Profile {
        private String type;
        private String name;
        private String owner;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomQuestion {
        private String name;
        private String type;
        private Integer position;
        private Boolean enabled;
        private Boolean required;
        
        @JsonProperty("answer_choices")
        private List<String> answerChoices;
        
        @JsonProperty("include_other")
        private Boolean includeOther;
    }
}
