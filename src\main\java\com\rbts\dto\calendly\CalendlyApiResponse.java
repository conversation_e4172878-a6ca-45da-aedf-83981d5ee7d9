package com.rbts.dto.calendly;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalendlyApiResponse<T> {
    
    private T resource;
    private List<T> collection;
    private Pagination pagination;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Pagination {
        private Integer count;
        
        @JsonProperty("next_page")
        private String nextPage;
        
        @JsonProperty("previous_page")
        private String previousPage;
        
        @JsonProperty("next_page_token")
        private String nextPageToken;
        
        @JsonProperty("previous_page_token")
        private String previousPageToken;
    }
}
