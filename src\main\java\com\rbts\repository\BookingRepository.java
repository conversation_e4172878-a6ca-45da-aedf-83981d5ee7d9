package com.rbts.repository;

import com.rbts.entity.Booking;
import com.rbts.entity.ContactDetails;
import com.rbts.entity.Session;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface BookingRepository extends JpaRepository<Booking, Long> {

    List<Booking> findByStudents(ContactDetails student);
    
    List<Booking> findBySession(Session session);
    
    Optional<Booking> findBySessionAndStudents(Session session, ContactDetails student);
    
    long countBySession(Session session);
    
    @Query("SELECT b FROM Booking b WHERE b.students = :student AND b.session.startTime >= :fromTime")
    List<Booking> findUpcomingBookingsByStudent(@Param("student") ContactDetails student, 
                                               @Param("fromTime") LocalDateTime fromTime);
    
    @Query("SELECT b FROM Booking b WHERE b.session = :session AND b.status.statusName = :statusName")
    List<Booking> findBySessionAndStatus(@Param("session") Session session, 
                                        @Param("statusName") String statusName);
    
    @Query("SELECT b FROM Booking b WHERE b.students = :student AND b.status.statusName = :statusName")
    List<Booking> findByStudentAndStatus(@Param("student") ContactDetails student, 
                                        @Param("statusName") String statusName);
    
    @Query("SELECT COUNT(b) FROM Booking b WHERE b.session.tutorId = :tutor AND b.session.startTime >= :fromTime")
    long countUpcomingBookingsByTutor(@Param("tutor") ContactDetails tutor, 
                                     @Param("fromTime") LocalDateTime fromTime);
}
