package com.rbts.dto.calendly;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalendlyOrganizationInvitation {
    
    private String uri;
    private String organization;
    private String email;
    private String status;
    
    @JsonProperty("created_at")
    private OffsetDateTime createdAt;
    
    @JsonProperty("updated_at")
    private OffsetDateTime updatedAt;
    
    @JsonProperty("last_sent_at")
    private OffsetDateTime lastSentAt;
    
    @JsonProperty("last_seen_at")
    private OffsetDateTime lastSeenAt;
    
    private String user;
    
    @JsonProperty("resource_type")
    private String resourceType;
}
