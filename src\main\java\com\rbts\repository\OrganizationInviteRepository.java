package com.rbts.repository;

import com.rbts.entity.ContactDetails;
import com.rbts.entity.OrganizationInvite;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface OrganizationInviteRepository extends JpaRepository<OrganizationInvite, Long> {

    Optional<OrganizationInvite> findByContactAndStatus(ContactDetails contact, String status);
    
    Optional<OrganizationInvite> findByEmailAndStatus(String email, String status);
    
    Optional<OrganizationInvite> findByCalendlyInvitationUri(String calendlyInvitationUri);
    
    List<OrganizationInvite> findByContact(ContactDetails contact);
    
    List<OrganizationInvite> findByStatus(String status);
    
    List<OrganizationInvite> findByStatusAndInvitedAtBefore(String status, LocalDateTime dateTime);
    
    @Query("SELECT oi FROM OrganizationInvite oi WHERE oi.contact.id = :contactId AND oi.status = 'ACCEPTED' AND oi.hasCalendarPermission = true")
    Optional<OrganizationInvite> findAcceptedInviteWithCalendarPermission(@Param("contactId") Long contactId);
    
    @Query("SELECT oi FROM OrganizationInvite oi WHERE oi.status = 'PENDING' AND oi.invitedAt < :cutoffDate")
    List<OrganizationInvite> findExpiredPendingInvites(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    boolean existsByContactAndStatus(ContactDetails contact, String status);
    
    boolean existsByEmailAndStatus(String email, String status);
}
