package com.rbts.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "calendly")
public class CalendlyProperties {
    
    private String apiUrl = "https://api.calendly.com";
    private String accessToken;
    private String organizationUri;
    private String webhookSigningKey;
    private int timeoutSeconds = 30;
    
    // API endpoints
    private Endpoints endpoints = new Endpoints();
    
    @Data
    public static class Endpoints {
        private String users = "/users";
        private String eventTypes = "/event_types";
        private String scheduledEvents = "/scheduled_events";
        private String organizationInvitations = "/organization_invitations";
        private String organizationMemberships = "/organization_memberships";
        private String userAvailabilitySchedules = "/user_availability_schedules";
        private String availabilityRules = "/availability_rules";
        private String webhookSubscriptions = "/webhook_subscriptions";
    }
}
