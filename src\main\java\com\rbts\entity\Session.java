package com.rbts.entity;

import com.rbts.entity.master.SessionType;
import com.rbts.entity.master.SubjectMaster;
import com.rbts.entity.master.SubjectSubCategory;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "sessions")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Session {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sessionSeqGen")
    @SequenceGenerator(name = "sessionSeqGen", sequenceName = "sessions_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @OneToOne
    @JoinColumn(name = "tutor_id", nullable = false)
    private ContactDetails tutorId;

    @NotNull
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "session_type_id", nullable = false)
    private SessionType sessionType;

    @NotNull
    @Column(name = "title",nullable = false)
    private String title;

    @NotNull
    @OneToOne
    @JoinColumn(name = "subject_id", nullable = false)
    private SubjectMaster subject;


    @OneToOne
    @JoinColumn(name = "subject_sub_category_id")
    private SubjectSubCategory subjectSubCategory;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @NotNull
    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;

    @NotNull
    @Column(name = "end_time", nullable = false)
    private LocalDateTime endTime;

    @Column(name = "session_price")
    private BigDecimal sessionPrice;

    @Column(name = "currency_code", length = 3)
    private String currencyCode;

    @Column(name = "min_students")
    private Long minStudents;

    @Column(name = "max_students")
    private Long maxStudents;

    @NotNull
    @OneToOne
    @JoinColumn(name = "session_location_id",nullable = false)
    private SessionLocation sessionLocation;
}
