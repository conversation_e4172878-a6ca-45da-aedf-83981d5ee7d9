package com.rbts.repository;

import com.rbts.entity.ContactDetails;
import com.rbts.entity.TutorAvailabilityOverride;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface TutorAvailabilityOverrideRepository extends JpaRepository<TutorAvailabilityOverride, Long> {

    List<TutorAvailabilityOverride> findByContact(ContactDetails contact);

    List<TutorAvailabilityOverride> findByContactAndOverrideDate(ContactDetails contact, Long overrideDate);

    List<TutorAvailabilityOverride> findByContactAndOverrideDateBetween(
            ContactDetails contact, Long startDate, Long endDate);
    
    Optional<TutorAvailabilityOverride> findByContactAndOverrideDateAndStartTime(
            ContactDetails contact, Long overrideDate, LocalDateTime startTime);
    
    List<TutorAvailabilityOverride> findByContactInAndOverrideDateBetweenAndIsActiveTrue(
            List<ContactDetails> contacts, Long startDate, Long endDate);
    
    @Query("SELECT tao FROM TutorAvailabilityOverride tao WHERE tao.contact = :contact " +
           "AND tao.overrideDate = :overrideDate AND tao.isActive = true " +
           "AND tao.startTime <= :dateTime AND tao.endTime >= :dateTime")
    List<TutorAvailabilityOverride> findOverlappingOverrides(
            @Param("contact") ContactDetails contact,
            @Param("overrideDate") Long overrideDate,
            @Param("dateTime") LocalDateTime dateTime);
    
    @Query("SELECT COUNT(tao) FROM TutorAvailabilityOverride tao WHERE tao.contact = :contact " +
           "AND tao.overrideDate BETWEEN :startDate AND :endDate AND tao.isActive = true")
    Long countActiveOverridesByContactAndDateRange(
            @Param("contact") ContactDetails contact,
            @Param("startDate") Long startDate,
            @Param("endDate") Long endDate);
    
    @Query("SELECT tao FROM TutorAvailabilityOverride tao WHERE tao.contact = :contact " +
           "AND tao.overrideDate >= :fromDate AND tao.isActive = true ORDER BY tao.overrideDate ASC")
    List<TutorAvailabilityOverride> findFutureOverridesByContact(
            @Param("contact") ContactDetails contact,
            @Param("fromDate") Long fromDate);
    
    @Query("SELECT tao FROM TutorAvailabilityOverride tao WHERE tao.overrideDate = :overrideDate " +
           "AND tao.isActive = true ORDER BY tao.startTime ASC")
    List<TutorAvailabilityOverride> findAllOverridesByDate(@Param("overrideDate") Long overrideDate);
    
    boolean existsByContactAndOverrideDateAndStartTimeAndIsActiveTrue(
            ContactDetails contact, Long overrideDate, LocalDateTime startTime);
}
