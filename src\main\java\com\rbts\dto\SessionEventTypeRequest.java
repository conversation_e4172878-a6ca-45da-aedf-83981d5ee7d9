package com.rbts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionEventTypeRequest {
    
    @NotNull
    private String title;
    
    @NotNull
    @Positive
    private Integer duration; // in minutes
    
    private String description;
    
    @NotNull
    private Long tutorId;
    
    @NotNull
    private Long sessionTypeId;
    
    @NotNull
    private Long subjectId;
    
    private Long subjectSubCategoryId;
    
    @NotNull
    private LocalDateTime startTime;
    
    @NotNull
    private LocalDateTime endTime;
    
    private BigDecimal sessionPrice;
    
    private String currencyCode;
    
    private Long minStudents;
    
    private Long maxStudents;
    
    @NotNull
    private Long sessionLocationId;
    
    private String color;
    
    @JsonProperty("internal_note")
    private String internalNote;
    
    // Calendly specific fields
    private String kind; // "solo" or "group"
    
    @JsonProperty("pooling_type")
    private String poolingType;
    
    private String type; // "StandardEventType" or "AdhocEventType"
    
    @JsonProperty("booking_method")
    private String bookingMethod;
    
    @JsonProperty("custom_questions")
    private List<CustomQuestion> customQuestions;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomQuestion {
        private String name;
        private String type;
        private Integer position;
        private Boolean enabled;
        private Boolean required;
        
        @JsonProperty("answer_choices")
        private List<String> answerChoices;
        
        @JsonProperty("include_other")
        private Boolean includeOther;
    }
}
