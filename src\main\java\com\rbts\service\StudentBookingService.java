package com.rbts.service;

import com.rbts.entity.Booking;
import com.rbts.entity.BookingOccurrence;
import com.rbts.entity.ContactDetails;
import com.rbts.entity.Session;
import com.rbts.entity.StatusMaster;
import com.rbts.repository.BookingRepository;
import com.rbts.repository.BookingOccurrenceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class StudentBookingService {

    private final BookingRepository bookingRepository;
    private final BookingOccurrenceRepository bookingOccurrenceRepository;

    /**
     * Create a booking for a student to enroll in a session
     */
    @Transactional
    public Booking createBooking(Session session, ContactDetails student, StatusMaster status, Long creditPointsUsed) {
        log.info("Creating booking for student: {} in session: {}", student.getId(), session.getId());
        
        // Check if student already has a booking for this session
        Optional<Booking> existingBooking = bookingRepository.findBySessionAndStudents(session, student);
        if (existingBooking.isPresent()) {
            log.warn("Student {} already has a booking for session {}", student.getId(), session.getId());
            throw new RuntimeException("Student already enrolled in this session");
        }
        
        // Create the booking
        Booking booking = Booking.builder()
                .session(session)
                .students(student)
                .status(status)
                .bookedAt(LocalDateTime.now())
                .creditPointsUsed(creditPointsUsed)
                .build();
        
        Booking savedBooking = bookingRepository.save(booking);
        log.info("Created booking with ID: {}", savedBooking.getId());
        
        // Create booking occurrence for the session time
        BookingOccurrence occurrence = BookingOccurrence.builder()
                .booking(savedBooking)
                .occurrenceTime(session.getStartTime())
                .endTime(session.getEndTime())
                .statusId(status)
                .build();
        
        bookingOccurrenceRepository.save(occurrence);
        log.info("Created booking occurrence for booking: {}", savedBooking.getId());
        
        return savedBooking;
    }

    /**
     * Get all bookings for a student
     */
    public List<Booking> getStudentBookings(ContactDetails student) {
        log.info("Getting bookings for student: {}", student.getId());
        return bookingRepository.findByStudents(student);
    }

    /**
     * Get all bookings for a session
     */
    public List<Booking> getSessionBookings(Session session) {
        log.info("Getting bookings for session: {}", session.getId());
        return bookingRepository.findBySession(session);
    }

    /**
     * Cancel a booking
     */
    @Transactional
    public boolean cancelBooking(Long bookingId, ContactDetails student) {
        log.info("Cancelling booking: {} for student: {}", bookingId, student.getId());
        
        Optional<Booking> bookingOpt = bookingRepository.findById(bookingId);
        if (bookingOpt.isEmpty()) {
            log.warn("Booking not found: {}", bookingId);
            return false;
        }
        
        Booking booking = bookingOpt.get();
        
        // Verify the booking belongs to the student
        if (!booking.getStudents().getId().equals(student.getId())) {
            log.warn("Attempted to cancel booking {} that doesn't belong to student {}", 
                    bookingId, student.getId());
            return false;
        }
        
        // Update booking status to cancelled (assuming there's a CANCELLED status)
        // For now, we'll delete the booking
        bookingRepository.delete(booking);
        log.info("Cancelled booking: {}", bookingId);
        
        return true;
    }

    /**
     * Check if a session has available spots
     */
    public boolean hasAvailableSpots(Session session) {
        List<Booking> bookings = bookingRepository.findBySession(session);
        long currentBookings = bookings.size();
        
        if (session.getMaxStudents() != null) {
            return currentBookings < session.getMaxStudents();
        }
        
        // If no max limit is set, assume unlimited
        return true;
    }

    /**
     * Get booking by ID
     */
    public Optional<Booking> getBooking(Long bookingId) {
        return bookingRepository.findById(bookingId);
    }

    /**
     * Check if student is enrolled in a session
     */
    public boolean isStudentEnrolled(Session session, ContactDetails student) {
        return bookingRepository.findBySessionAndStudents(session, student).isPresent();
    }

    /**
     * Get booking count for a session
     */
    public long getBookingCount(Session session) {
        return bookingRepository.countBySession(session);
    }
}
