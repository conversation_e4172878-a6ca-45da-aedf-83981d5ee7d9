package com.rbts.service;

import com.rbts.dto.calendly.CalendlyEventType;
import com.rbts.entity.ContactDetails;
import com.rbts.entity.Session;
import com.rbts.repository.SessionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class SessionService {

    private final SessionRepository sessionRepository;

    @Transactional
    public Session createSessionFromCalendlyEventType(CalendlyEventType eventType, Session sessionData) {
        log.info("Creating session from Calendly event type: {}", eventType.getUri());
        
        // Update session with Calendly data
        sessionData.setEventTypeUri(eventType.getUri());
        sessionData.setSchedulingUrl(eventType.getSchedulingUrl());
        sessionData.setOwnerUri(eventType.getProfile() != null ? eventType.getProfile().getOwner() : null);
        sessionData.setActive(eventType.getActive() != null ? eventType.getActive() : true);
        
        // Set duration if not already set
        if (eventType.getDuration() != null && sessionData.getEndTime() == null) {
            sessionData.setEndTime(sessionData.getStartTime().plusMinutes(eventType.getDuration()));
        }
        
        Session savedSession = sessionRepository.save(sessionData);
        log.info("Created session with ID: {} linked to Calendly event type: {}", 
                savedSession.getId(), eventType.getUri());
        
        return savedSession;
    }

    @Transactional
    public Session updateSessionFromCalendlyEventType(CalendlyEventType eventType) {
        log.info("Updating session from Calendly event type: {}", eventType.getUri());
        
        Optional<Session> sessionOpt = sessionRepository.findByEventTypeUri(eventType.getUri());
        if (sessionOpt.isEmpty()) {
            log.warn("No session found for Calendly event type: {}", eventType.getUri());
            return null;
        }
        
        Session session = sessionOpt.get();
        
        // Update session with latest Calendly data
        session.setTitle(eventType.getName());
        session.setDescription(eventType.getDescriptionPlain());
        session.setSchedulingUrl(eventType.getSchedulingUrl());
        session.setActive(eventType.getActive() != null ? eventType.getActive() : true);
        
        // Update duration if changed
        if (eventType.getDuration() != null) {
            LocalDateTime newEndTime = session.getStartTime().plusMinutes(eventType.getDuration());
            session.setEndTime(newEndTime);
        }
        
        Session updatedSession = sessionRepository.save(session);
        log.info("Updated session with ID: {} from Calendly event type", updatedSession.getId());
        
        return updatedSession;
    }

    @Transactional
    public void deleteSessionByEventTypeUri(String eventTypeUri) {
        log.info("Deleting session for Calendly event type: {}", eventTypeUri);
        
        Optional<Session> sessionOpt = sessionRepository.findByEventTypeUri(eventTypeUri);
        if (sessionOpt.isPresent()) {
            Session session = sessionOpt.get();
            session.setActive(false); // Soft delete
            sessionRepository.save(session);
            log.info("Deactivated session with ID: {} for event type: {}", session.getId(), eventTypeUri);
        } else {
            log.warn("No session found for Calendly event type: {}", eventTypeUri);
        }
    }

    public Optional<Session> findByEventTypeUri(String eventTypeUri) {
        return sessionRepository.findByEventTypeUri(eventTypeUri);
    }

    public List<Session> findByTutor(ContactDetails tutor) {
        return sessionRepository.findByTutorId(tutor);
    }

    public List<Session> findActiveFutureSessionsByTutor(ContactDetails tutor) {
        return sessionRepository.findActiveFutureSessionsByTutor(tutor, LocalDateTime.now());
    }

    public List<Session> findByTutorAndTimeRange(ContactDetails tutor, LocalDateTime startTime, LocalDateTime endTime) {
        return sessionRepository.findByTutorIdAndTimeRange(tutor, startTime, endTime);
    }

    public Optional<Session> findById(Long id) {
        return sessionRepository.findById(id);
    }

    @Transactional
    public Session save(Session session) {
        return sessionRepository.save(session);
    }

    @Transactional
    public void deleteById(Long id) {
        sessionRepository.deleteById(id);
    }

    public boolean existsByEventTypeUri(String eventTypeUri) {
        return sessionRepository.existsByEventTypeUri(eventTypeUri);
    }

    public Long countActiveSessionsByTutor(ContactDetails tutor) {
        return sessionRepository.countActiveSessionsByTutor(tutor);
    }

    // Helper method to convert Calendly EventType to Session data
    public Session convertCalendlyEventTypeToSession(CalendlyEventType eventType, ContactDetails tutor) {
        return Session.builder()
                .title(eventType.getName())
                .description(eventType.getDescriptionPlain())
                .eventTypeUri(eventType.getUri())
                .schedulingUrl(eventType.getSchedulingUrl())
                .ownerUri(eventType.getProfile() != null ? eventType.getProfile().getOwner() : null)
                .active(eventType.getActive() != null ? eventType.getActive() : true)
                .tutorId(tutor)
                .startTime(eventType.getCreatedAt() != null ? 
                          eventType.getCreatedAt().atZoneSameInstant(ZoneOffset.UTC).toLocalDateTime() : 
                          LocalDateTime.now())
                .endTime(eventType.getCreatedAt() != null && eventType.getDuration() != null ? 
                        eventType.getCreatedAt().atZoneSameInstant(ZoneOffset.UTC).toLocalDateTime()
                                .plusMinutes(eventType.getDuration()) : 
                        LocalDateTime.now().plusHours(1))
                .build();
    }
}
