package com.rbts.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;

@Entity
@Table(name = "organization_invites")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrganizationInvite {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "orgInviteSeqGen")
    @SequenceGenerator(name = "orgInviteSeqGen", sequenceName = "organization_invite_seq", allocationSize = 1)
    @Column(name = "id")
    private Long id;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "contact_id", nullable = false)
    private ContactDetails contact;

    @NotNull
    @Column(name = "email", nullable = false)
    private String email;

    @Column(name = "calendly_invitation_uri", columnDefinition = "TEXT")
    private String calendlyInvitationUri;

    @NotNull
    @Column(name = "status", nullable = false)
    private String status; // PENDING, ACCEPTED, DECLINED, REVOKED

    @NotNull
    @Column(name = "invited_at", nullable = false)
    private LocalDateTime invitedAt;

    @Column(name = "accepted_at")
    private LocalDateTime acceptedAt;

    @Column(name = "declined_at")
    private LocalDateTime declinedAt;

    @Column(name = "revoked_at")
    private LocalDateTime revokedAt;

    @Column(name = "last_sent_at")
    private LocalDateTime lastSentAt;

    @Column(name = "calendly_user_uri", columnDefinition = "TEXT")
    private String calendlyUserUri;

    @Column(name = "has_calendar_permission")
    private Boolean hasCalendarPermission;

    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "status_id", nullable = false)
    private StatusMaster statusMaster;
}
