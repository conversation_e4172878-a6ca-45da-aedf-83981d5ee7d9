package com.rbts.dto.calendly;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalendlyAvailability {
    
    @JsonProperty("busy_times")
    private List<BusyTime> busyTimes;
    
    @JsonProperty("available_times")
    private List<AvailableTime> availableTimes;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusyTime {
        
        @JsonProperty("start_time")
        private OffsetDateTime startTime;
        
        @JsonProperty("end_time")
        private OffsetDateTime endTime;
        
        private String status;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AvailableTime {
        
        @JsonProperty("start_time")
        private OffsetDateTime startTime;
        
        @JsonProperty("end_time")
        private OffsetDateTime endTime;
        
        private String status;
        
        @JsonProperty("invitees_remaining")
        private Integer inviteesRemaining;
    }
}
