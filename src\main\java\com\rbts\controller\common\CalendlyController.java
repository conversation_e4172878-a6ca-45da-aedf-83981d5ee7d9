package com.rbts.controller.common;

import com.rbts.dto.SessionEventTypeRequest;
import com.rbts.dto.SessionEventTypeResponse;
import com.rbts.dto.calendly.CalendlyEventType;
import com.rbts.dto.calendly.CalendlyOrganizationInvitation;
import com.rbts.dto.calendly.request.CreateEventTypeRequest;
import com.rbts.dto.calendly.request.CreateOrganizationInvitationRequest;
import com.rbts.dto.calendly.request.UpdateEventTypeRequest;
import com.rbts.entity.ContactDetails;
import com.rbts.entity.OrganizationInvite;
import com.rbts.entity.Session;
import com.rbts.entity.SessionLocation;
import com.rbts.entity.StatusMaster;
import com.rbts.entity.master.SessionType;
import com.rbts.entity.master.SubjectMaster;
import com.rbts.entity.master.SubjectSubCategory;
import com.rbts.repository.OrganizationInviteRepository;
import com.rbts.service.SessionService;
import com.rbts.service.calendly.CalendlyService;
import com.rbts.service.common.DynamicEntityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/calendly")
@RequiredArgsConstructor
public class CalendlyController {

    private final CalendlyService calendlyService;
    private final SessionService sessionService;
    private final DynamicEntityService dynamicEntityService;
    private final OrganizationInviteRepository organizationInviteRepository;

    /**
     * Create a new EventType in Calendly and corresponding Session in database
     */
    @PostMapping("/event-types")
    public Mono<ResponseEntity<SessionEventTypeResponse>> createEventType(@Valid @RequestBody SessionEventTypeRequest request) {
        log.info("Creating new event type for tutor: {}", request.getTutorId());

        return Mono.fromCallable(() -> {
            // Get tutor details
            List<Map<String, Object>> tutorData = dynamicEntityService.getEntitiesByField("ContactDetails", "id", request.getTutorId().toString());
            if (tutorData.isEmpty()) {
                throw new RuntimeException("Tutor not found with ID: " + request.getTutorId());
            }

            // Get required entities
            ContactDetails tutor = getContactDetailsById(request.getTutorId());
            SessionType sessionType = getSessionTypeById(request.getSessionTypeId());
            SubjectMaster subject = getSubjectById(request.getSubjectId());
            SubjectSubCategory subjectSubCategory = request.getSubjectSubCategoryId() != null ?
                    getSubjectSubCategoryById(request.getSubjectSubCategoryId()) : null;
            SessionLocation sessionLocation = getSessionLocationById(request.getSessionLocationId());

            // Create Session entity first
            Session session = Session.builder()
                    .title(request.getTitle())
                    .description(request.getDescription())
                    .startTime(request.getStartTime())
                    .endTime(request.getEndTime())
                    .sessionPrice(request.getSessionPrice())
                    .currencyCode(request.getCurrencyCode())
                    .minStudents(request.getMinStudents())
                    .maxStudents(request.getMaxStudents())
                    .active(true)
                    .tutorId(tutor)
                    .sessionType(sessionType)
                    .subject(subject)
                    .subjectSubCategory(subjectSubCategory)
                    .sessionLocation(sessionLocation)
                    .build();

            return session;
        })
        .flatMap(session -> {
            // Create Calendly EventType request
            CreateEventTypeRequest calendlyRequest = CreateEventTypeRequest.builder()
                    .name(request.getTitle())
                    .duration(request.getDuration())
                    .kind(request.getKind() != null ? request.getKind() : "solo")
                    .poolingType(request.getPoolingType())
                    .type(request.getType() != null ? request.getType() : "StandardEventType")
                    .color(request.getColor())
                    .internalNote(request.getInternalNote())
                    .descriptionPlain(request.getDescription())
                    .bookingMethod(request.getBookingMethod())
                    .customQuestions(request.getCustomQuestions() != null ?
                            request.getCustomQuestions().stream()
                                    .map(cq -> CreateEventTypeRequest.CustomQuestion.builder()
                                            .name(cq.getName())
                                            .type(cq.getType())
                                            .position(cq.getPosition())
                                            .enabled(cq.getEnabled())
                                            .required(cq.getRequired())
                                            .answerChoices(cq.getAnswerChoices())
                                            .includeOther(cq.getIncludeOther())
                                            .build())
                                    .collect(Collectors.toList()) : null)
                    .profile(CreateEventTypeRequest.Profile.builder()
                            .type("User")
                            .name(session.getTutorId().getDisplayName())
                            .build())
                    .build();

            // Create EventType in Calendly
            return calendlyService.createEventType(calendlyRequest)
                    .map(calendlyEventType -> {
                        // Update session with Calendly data and save
                        Session savedSession = sessionService.createSessionFromCalendlyEventType(calendlyEventType, session);
                        return buildSessionEventTypeResponse(savedSession, calendlyEventType);
                    });
        })
        .map(response -> ResponseEntity.status(HttpStatus.CREATED).body(response))
        .onErrorResume(error -> {
            log.error("Failed to create event type", error);
            return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build());
        });
    }

    /**
     * Get EventType by ID (returns both Session and Calendly data)
     */
    @GetMapping("/event-types/{sessionId}")
    public Mono<ResponseEntity<SessionEventTypeResponse>> getEventType(@PathVariable Long sessionId) {
        log.info("Getting event type for session: {}", sessionId);

        return Mono.fromCallable(() -> {
            Optional<Session> sessionOpt = sessionService.findById(sessionId);
            if (sessionOpt.isEmpty()) {
                throw new RuntimeException("Session not found with ID: " + sessionId);
            }
            return sessionOpt.get();
        })
        .flatMap(session -> {
            if (session.getEventTypeUri() != null) {
                return calendlyService.getEventType(session.getEventTypeUri())
                        .map(calendlyEventType -> buildSessionEventTypeResponse(session, calendlyEventType));
            } else {
                return Mono.just(buildSessionEventTypeResponse(session, null));
            }
        })
        .map(response -> ResponseEntity.ok(response))
        .onErrorResume(error -> {
            log.error("Failed to get event type for session: {}", sessionId, error);
            return Mono.just(ResponseEntity.status(HttpStatus.NOT_FOUND).build());
        });
    }

    /**
     * Update EventType in Calendly and corresponding Session in database
     */
    @PutMapping("/event-types/{sessionId}")
    public Mono<ResponseEntity<SessionEventTypeResponse>> updateEventType(
            @PathVariable Long sessionId,
            @Valid @RequestBody SessionEventTypeRequest request) {
        log.info("Updating event type for session: {}", sessionId);

        return Mono.fromCallable(() -> {
            Optional<Session> sessionOpt = sessionService.findById(sessionId);
            if (sessionOpt.isEmpty()) {
                throw new RuntimeException("Session not found with ID: " + sessionId);
            }

            Session session = sessionOpt.get();
            if (session.getEventTypeUri() == null) {
                throw new RuntimeException("Session is not linked to a Calendly event type");
            }

            // Update session data
            session.setTitle(request.getTitle());
            session.setDescription(request.getDescription());
            session.setStartTime(request.getStartTime());
            session.setEndTime(request.getEndTime());
            session.setSessionPrice(request.getSessionPrice());
            session.setCurrencyCode(request.getCurrencyCode());
            session.setMinStudents(request.getMinStudents());
            session.setMaxStudents(request.getMaxStudents());

            return session;
        })
        .flatMap(session -> {
            // Create Calendly update request
            UpdateEventTypeRequest calendlyRequest = UpdateEventTypeRequest.builder()
                    .name(request.getTitle())
                    .duration(request.getDuration())
                    .active(true)
                    .color(request.getColor())
                    .internalNote(request.getInternalNote())
                    .descriptionPlain(request.getDescription())
                    .customQuestions(request.getCustomQuestions() != null ?
                            request.getCustomQuestions().stream()
                                    .map(cq -> UpdateEventTypeRequest.CustomQuestion.builder()
                                            .name(cq.getName())
                                            .type(cq.getType())
                                            .position(cq.getPosition())
                                            .enabled(cq.getEnabled())
                                            .required(cq.getRequired())
                                            .answerChoices(cq.getAnswerChoices())
                                            .includeOther(cq.getIncludeOther())
                                            .build())
                                    .collect(Collectors.toList()) : null)
                    .build();

            // Update EventType in Calendly
            return calendlyService.updateEventType(session.getEventTypeUri(), calendlyRequest)
                    .map(calendlyEventType -> {
                        // Save updated session
                        Session savedSession = sessionService.save(session);
                        return buildSessionEventTypeResponse(savedSession, calendlyEventType);
                    });
        })
        .map(response -> ResponseEntity.ok(response))
        .onErrorResume(error -> {
            log.error("Failed to update event type for session: {}", sessionId, error);
            return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build());
        });
    }

    /**
     * Delete EventType from Calendly and deactivate corresponding Session
     */
    @DeleteMapping("/event-types/{sessionId}")
    public Mono<ResponseEntity<Void>> deleteEventType(@PathVariable Long sessionId) {
        log.info("Deleting event type for session: {}", sessionId);

        return Mono.fromCallable(() -> {
            Optional<Session> sessionOpt = sessionService.findById(sessionId);
            if (sessionOpt.isEmpty()) {
                throw new RuntimeException("Session not found with ID: " + sessionId);
            }
            return sessionOpt.get();
        })
        .flatMap(session -> {
            if (session.getEventTypeUri() != null) {
                return calendlyService.deleteEventType(session.getEventTypeUri())
                        .then(Mono.fromRunnable(() -> sessionService.deleteSessionByEventTypeUri(session.getEventTypeUri())));
            } else {
                // Just deactivate the session if no Calendly event type
                session.setActive(false);
                sessionService.save(session);
                return Mono.empty();
            }
        })
        .then(Mono.just(ResponseEntity.noContent().<Void>build()))
        .onErrorResume(error -> {
            log.error("Failed to delete event type for session: {}", sessionId, error);
            return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build());
        });
    }

    /**
     * Get all EventTypes for a tutor
     */
    @GetMapping("/event-types/tutor/{tutorId}")
    public ResponseEntity<List<SessionEventTypeResponse>> getTutorEventTypes(@PathVariable Long tutorId) {
        log.info("Getting event types for tutor: {}", tutorId);

        try {
            ContactDetails tutor = getContactDetailsById(tutorId);
            List<Session> sessions = sessionService.findByTutor(tutor);

            List<SessionEventTypeResponse> responses = sessions.stream()
                    .map(session -> buildSessionEventTypeResponse(session, null))
                    .collect(Collectors.toList());

            return ResponseEntity.ok(responses);
        } catch (Exception e) {
            log.error("Failed to get event types for tutor: {}", tutorId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    // Helper methods
    private ContactDetails getContactDetailsById(Long id) {
        try {
            List<Map<String, Object>> data = dynamicEntityService.getEntitiesByField("ContactDetails", "id", id.toString());
            if (data.isEmpty()) {
                throw new RuntimeException("ContactDetails not found with ID: " + id);
            }
            // Convert map to entity - this is a simplified approach
            // In a real implementation, you might want to use a proper mapper
            return ContactDetails.builder()
                    .id(id)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to get ContactDetails with ID: " + id, e);
        }
    }

    private SessionType getSessionTypeById(Long id) {
        try {
            List<Map<String, Object>> data = dynamicEntityService.getEntitiesByField("SessionType", "id", id.toString());
            if (data.isEmpty()) {
                throw new RuntimeException("SessionType not found with ID: " + id);
            }
            return SessionType.builder()
                    .id(id)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to get SessionType with ID: " + id, e);
        }
    }

    private SubjectMaster getSubjectById(Long id) {
        try {
            List<Map<String, Object>> data = dynamicEntityService.getEntitiesByField("SubjectMaster", "id", id.toString());
            if (data.isEmpty()) {
                throw new RuntimeException("SubjectMaster not found with ID: " + id);
            }
            return SubjectMaster.builder()
                    .id(id)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to get SubjectMaster with ID: " + id, e);
        }
    }

    private SubjectSubCategory getSubjectSubCategoryById(Long id) {
        try {
            List<Map<String, Object>> data = dynamicEntityService.getEntitiesByField("SubjectSubCategory", "id", id.toString());
            if (data.isEmpty()) {
                throw new RuntimeException("SubjectSubCategory not found with ID: " + id);
            }
            return SubjectSubCategory.builder()
                    .id(id)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to get SubjectSubCategory with ID: " + id, e);
        }
    }

    private SessionLocation getSessionLocationById(Long id) {
        try {
            List<Map<String, Object>> data = dynamicEntityService.getEntitiesByField("SessionLocation", "id", id.toString());
            if (data.isEmpty()) {
                throw new RuntimeException("SessionLocation not found with ID: " + id);
            }
            return SessionLocation.builder()
                    .id(id)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to get SessionLocation with ID: " + id, e);
        }
    }

    private SessionEventTypeResponse buildSessionEventTypeResponse(Session session, CalendlyEventType calendlyEventType) {
        SessionEventTypeResponse.SessionEventTypeResponseBuilder builder = SessionEventTypeResponse.builder()
                .sessionId(session.getId())
                .title(session.getTitle())
                .description(session.getDescription())
                .startTime(session.getStartTime())
                .endTime(session.getEndTime())
                .sessionPrice(session.getSessionPrice())
                .currencyCode(session.getCurrencyCode())
                .minStudents(session.getMinStudents())
                .maxStudents(session.getMaxStudents())
                .active(session.isActive())
                .tutorId(session.getTutorId().getId())
                .tutorName(session.getTutorId().getDisplayName())
                .sessionTypeId(session.getSessionType().getId())
                .subjectId(session.getSubject().getId())
                .sessionLocationId(session.getSessionLocation().getId())
                .eventTypeUri(session.getEventTypeUri())
                .ownerUri(session.getOwnerUri())
                .schedulingUrl(session.getSchedulingUrl());

        // Add Calendly-specific data if available
        if (calendlyEventType != null) {
            builder.duration(calendlyEventType.getDuration())
                    .calendlyCreatedAt(calendlyEventType.getCreatedAt())
                    .calendlyUpdatedAt(calendlyEventType.getUpdatedAt())
                    .color(calendlyEventType.getColor())
                    .kind(calendlyEventType.getKind())
                    .poolingType(calendlyEventType.getPoolingType())
                    .type(calendlyEventType.getType())
                    .bookingMethod(calendlyEventType.getBookingMethod());
        }

        // Add subject sub-category if available
        if (session.getSubjectSubCategory() != null) {
            builder.subjectSubCategoryId(session.getSubjectSubCategory().getId());
        }

        return builder.build();
    }

    // ===== ORGANIZATION INVITE ENDPOINTS =====

    /**
     * Create organization invitation for a user
     */
    @PostMapping("/organization-invites")
    public Mono<ResponseEntity<Map<String, Object>>> createOrganizationInvite(@RequestBody Map<String, Object> request) {
        log.info("Creating organization invitation for user: {}", request.get("contactId"));

        return Mono.fromCallable(() -> {
            Long contactId = Long.valueOf(request.get("contactId").toString());
            String email = request.get("email").toString();

            // Get contact details
            ContactDetails contact = getContactDetailsById(contactId);

            // Check if there's already a pending invitation
            if (organizationInviteRepository.existsByContactAndStatus(contact, "PENDING")) {
                throw new RuntimeException("User already has a pending organization invitation");
            }

            return Map.of("contact", contact, "email", email);
        })
        .flatMap(data -> {
            ContactDetails contact = (ContactDetails) data.get("contact");
            String email = (String) data.get("email");

            // Create Calendly organization invitation
            CreateOrganizationInvitationRequest calendlyRequest = CreateOrganizationInvitationRequest.builder()
                    .email(email)
                    .build();

            return calendlyService.createOrganizationInvitation(calendlyRequest)
                    .map(calendlyInvitation -> {
                        // Create local organization invite record
                        StatusMaster statusMaster = getStatusMasterByName("ACTIVE");

                        OrganizationInvite orgInvite = OrganizationInvite.builder()
                                .contact(contact)
                                .email(email)
                                .calendlyInvitationUri(calendlyInvitation.getUri())
                                .status("PENDING")
                                .invitedAt(java.time.LocalDateTime.now())
                                .lastSentAt(calendlyInvitation.getLastSentAt() != null ?
                                          calendlyInvitation.getLastSentAt().toLocalDateTime() :
                                          java.time.LocalDateTime.now())
                                .hasCalendarPermission(false)
                                .statusMaster(statusMaster)
                                .build();

                        OrganizationInvite savedInvite = organizationInviteRepository.save(orgInvite);

                        return Map.of(
                                "inviteId", savedInvite.getId(),
                                "calendlyInvitationUri", calendlyInvitation.getUri(),
                                "email", email,
                                "status", "PENDING",
                                "invitedAt", savedInvite.getInvitedAt(),
                                "message", "Organization invitation sent successfully"
                        );
                    });
        })
        .map(response -> ResponseEntity.status(HttpStatus.CREATED).body(response))
        .onErrorResume(error -> {
            log.error("Failed to create organization invitation", error);
            return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", error.getMessage())));
        });
    }

    /**
     * Get organization invitation status
     */
    @GetMapping("/organization-invites/{inviteId}")
    public Mono<ResponseEntity<Map<String, Object>>> getOrganizationInvite(@PathVariable Long inviteId) {
        log.info("Getting organization invitation: {}", inviteId);

        return Mono.fromCallable(() -> {
            Optional<OrganizationInvite> inviteOpt = organizationInviteRepository.findById(inviteId);
            if (inviteOpt.isEmpty()) {
                throw new RuntimeException("Organization invitation not found with ID: " + inviteId);
            }
            return inviteOpt.get();
        })
        .flatMap(invite -> {
            if (invite.getCalendlyInvitationUri() != null) {
                return calendlyService.getOrganizationInvitation(invite.getCalendlyInvitationUri())
                        .map(calendlyInvitation -> {
                            // Update local status if needed
                            if (!invite.getStatus().equals(calendlyInvitation.getStatus())) {
                                invite.setStatus(calendlyInvitation.getStatus());
                                if ("ACCEPTED".equals(calendlyInvitation.getStatus())) {
                                    invite.setAcceptedAt(java.time.LocalDateTime.now());
                                    invite.setCalendlyUserUri(calendlyInvitation.getUser());
                                }
                                organizationInviteRepository.save(invite);
                            }

                            return Map.of(
                                    "inviteId", invite.getId(),
                                    "email", invite.getEmail(),
                                    "status", invite.getStatus(),
                                    "invitedAt", invite.getInvitedAt(),
                                    "acceptedAt", invite.getAcceptedAt(),
                                    "calendlyUserUri", invite.getCalendlyUserUri(),
                                    "hasCalendarPermission", invite.getHasCalendarPermission(),
                                    "calendlyInvitationUri", invite.getCalendlyInvitationUri()
                            );
                        });
            } else {
                return Mono.just(Map.of(
                        "inviteId", invite.getId(),
                        "email", invite.getEmail(),
                        "status", invite.getStatus(),
                        "invitedAt", invite.getInvitedAt(),
                        "message", "No Calendly invitation URI found"
                ));
            }
        })
        .map(response -> ResponseEntity.ok(response))
        .onErrorResume(error -> {
            log.error("Failed to get organization invitation: {}", inviteId, error);
            return Mono.just(ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", error.getMessage())));
        });
    }

    private StatusMaster getStatusMasterByName(String statusName) {
        try {
            List<Map<String, Object>> data = dynamicEntityService.getEntitiesByField("StatusMaster", "statusName", statusName);
            if (data.isEmpty()) {
                throw new RuntimeException("StatusMaster not found with name: " + statusName);
            }
            return StatusMaster.builder()
                    .id(Long.valueOf(data.get(0).get("id").toString()))
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to get StatusMaster with name: " + statusName, e);
        }
    }
}
