package com.rbts.controller.common;

import com.rbts.dto.AvailabilityOverrideRequest;
import com.rbts.dto.AvailabilityOverrideResponse;
import com.rbts.dto.SessionEventTypeRequest;
import com.rbts.dto.SessionEventTypeResponse;
import com.rbts.dto.SignupRequest;
import com.rbts.dto.calendly.CalendlyEventType;
import com.rbts.dto.calendly.CalendlyOrganizationInvitation;
import com.rbts.dto.calendly.request.CreateEventTypeRequest;
import com.rbts.dto.calendly.request.CreateOrganizationInvitationRequest;
import com.rbts.dto.calendly.request.UpdateEventTypeRequest;
import com.rbts.entity.ContactDetails;
import com.rbts.entity.OrganizationInvite;
import com.rbts.entity.Session;
import com.rbts.entity.SessionLocation;
import com.rbts.entity.StatusMaster;
import com.rbts.entity.TutorAvailabilityOverride;
import com.rbts.entity.master.SessionType;
import com.rbts.entity.master.SubjectMaster;
import com.rbts.entity.master.SubjectSubCategory;
import com.rbts.repository.OrganizationInviteRepository;
import com.rbts.service.SessionService;
import com.rbts.service.TutorAvailabilityService;
import com.rbts.service.auth.EnhancedRegistrationService;
import com.rbts.service.calendly.CalendlyService;
import com.rbts.service.common.DynamicEntityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/calendly")
@RequiredArgsConstructor
public class CalendlyController {

    private final CalendlyService calendlyService;
    private final SessionService sessionService;
    private final DynamicEntityService dynamicEntityService;
    private final OrganizationInviteRepository organizationInviteRepository;
    private final EnhancedRegistrationService enhancedRegistrationService;
    private final TutorAvailabilityService tutorAvailabilityService;

    /**
     * Create a new EventType in Calendly and corresponding Session in database
     */
    @PostMapping("/event-types")
    public Mono<ResponseEntity<SessionEventTypeResponse>> createEventType(@Valid @RequestBody SessionEventTypeRequest request) {
        log.info("Creating new event type for tutor: {}", request.getTutorId());

        return Mono.fromCallable(() -> {
            // Get tutor details
            List<Map<String, Object>> tutorData = dynamicEntityService.getEntitiesByField("ContactDetails", "id", request.getTutorId().toString());
            if (tutorData.isEmpty()) {
                throw new RuntimeException("Tutor not found with ID: " + request.getTutorId());
            }

            // Get required entities
            ContactDetails tutor = getContactDetailsById(request.getTutorId());
            SessionType sessionType = getSessionTypeById(request.getSessionTypeId());
            SubjectMaster subject = getSubjectById(request.getSubjectId());
            SubjectSubCategory subjectSubCategory = request.getSubjectSubCategoryId() != null ?
                    getSubjectSubCategoryById(request.getSubjectSubCategoryId()) : null;
            SessionLocation sessionLocation = getSessionLocationById(request.getSessionLocationId());

            // Create Session entity first
            Session session = Session.builder()
                    .title(request.getTitle())
                    .description(request.getDescription())
                    .startTime(request.getStartTime())
                    .endTime(request.getEndTime())
                    .sessionPrice(request.getSessionPrice())
                    .currencyCode(request.getCurrencyCode())
                    .minStudents(request.getMinStudents())
                    .maxStudents(request.getMaxStudents())
                    .active(true)
                    .tutorId(tutor)
                    .sessionType(sessionType)
                    .subject(subject)
                    .subjectSubCategory(subjectSubCategory)
                    .sessionLocation(sessionLocation)
                    .build();

            return session;
        })
        .flatMap(session -> {
            // Create Calendly EventType request
            CreateEventTypeRequest calendlyRequest = CreateEventTypeRequest.builder()
                    .name(request.getTitle())
                    .duration(request.getDuration())
                    .kind(request.getKind() != null ? request.getKind() : "solo")
                    .poolingType(request.getPoolingType())
                    .type(request.getType() != null ? request.getType() : "StandardEventType")
                    .color(request.getColor())
                    .internalNote(request.getInternalNote())
                    .descriptionPlain(request.getDescription())
                    .bookingMethod(request.getBookingMethod())
                    .customQuestions(request.getCustomQuestions() != null ?
                            request.getCustomQuestions().stream()
                                    .map(cq -> CreateEventTypeRequest.CustomQuestion.builder()
                                            .name(cq.getName())
                                            .type(cq.getType())
                                            .position(cq.getPosition())
                                            .enabled(cq.getEnabled())
                                            .required(cq.getRequired())
                                            .answerChoices(cq.getAnswerChoices())
                                            .includeOther(cq.getIncludeOther())
                                            .build())
                                    .collect(Collectors.toList()) : null)
                    .profile(CreateEventTypeRequest.Profile.builder()
                            .type("User")
                            .name(session.getTutorId().getDisplayName())
                            .build())
                    .build();

            // Create EventType in Calendly
            return calendlyService.createEventType(calendlyRequest)
                    .map(calendlyEventType -> {
                        // Update session with Calendly data and save
                        Session savedSession = sessionService.createSessionFromCalendlyEventType(calendlyEventType, session);
                        return buildSessionEventTypeResponse(savedSession, calendlyEventType);
                    });
        })
        .map(response -> ResponseEntity.status(HttpStatus.CREATED).body(response))
        .onErrorResume(error -> {
            log.error("Failed to create event type", error);
            return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build());
        });
    }

    /**
     * Get EventType by ID (returns both Session and Calendly data)
     */
    @GetMapping("/event-types/{sessionId}")
    public Mono<ResponseEntity<SessionEventTypeResponse>> getEventType(@PathVariable Long sessionId) {
        log.info("Getting event type for session: {}", sessionId);

        return Mono.fromCallable(() -> {
            Optional<Session> sessionOpt = sessionService.findById(sessionId);
            if (sessionOpt.isEmpty()) {
                throw new RuntimeException("Session not found with ID: " + sessionId);
            }
            return sessionOpt.get();
        })
        .flatMap(session -> {
            if (session.getEventTypeUri() != null) {
                return calendlyService.getEventType(session.getEventTypeUri())
                        .map(calendlyEventType -> buildSessionEventTypeResponse(session, calendlyEventType));
            } else {
                return Mono.just(buildSessionEventTypeResponse(session, null));
            }
        })
        .map(response -> ResponseEntity.ok(response))
        .onErrorResume(error -> {
            log.error("Failed to get event type for session: {}", sessionId, error);
            return Mono.just(ResponseEntity.status(HttpStatus.NOT_FOUND).build());
        });
    }

    /**
     * Update EventType in Calendly and corresponding Session in database
     */
    @PutMapping("/event-types/{sessionId}")
    public Mono<ResponseEntity<SessionEventTypeResponse>> updateEventType(
            @PathVariable Long sessionId,
            @Valid @RequestBody SessionEventTypeRequest request) {
        log.info("Updating event type for session: {}", sessionId);

        return Mono.fromCallable(() -> {
            Optional<Session> sessionOpt = sessionService.findById(sessionId);
            if (sessionOpt.isEmpty()) {
                throw new RuntimeException("Session not found with ID: " + sessionId);
            }

            Session session = sessionOpt.get();
            if (session.getEventTypeUri() == null) {
                throw new RuntimeException("Session is not linked to a Calendly event type");
            }

            // Update session data
            session.setTitle(request.getTitle());
            session.setDescription(request.getDescription());
            session.setStartTime(request.getStartTime());
            session.setEndTime(request.getEndTime());
            session.setSessionPrice(request.getSessionPrice());
            session.setCurrencyCode(request.getCurrencyCode());
            session.setMinStudents(request.getMinStudents());
            session.setMaxStudents(request.getMaxStudents());

            return session;
        })
        .flatMap(session -> {
            // Create Calendly update request
            UpdateEventTypeRequest calendlyRequest = UpdateEventTypeRequest.builder()
                    .name(request.getTitle())
                    .duration(request.getDuration())
                    .active(true)
                    .color(request.getColor())
                    .internalNote(request.getInternalNote())
                    .descriptionPlain(request.getDescription())
                    .customQuestions(request.getCustomQuestions() != null ?
                            request.getCustomQuestions().stream()
                                    .map(cq -> UpdateEventTypeRequest.CustomQuestion.builder()
                                            .name(cq.getName())
                                            .type(cq.getType())
                                            .position(cq.getPosition())
                                            .enabled(cq.getEnabled())
                                            .required(cq.getRequired())
                                            .answerChoices(cq.getAnswerChoices())
                                            .includeOther(cq.getIncludeOther())
                                            .build())
                                    .collect(Collectors.toList()) : null)
                    .build();

            // Update EventType in Calendly
            return calendlyService.updateEventType(session.getEventTypeUri(), calendlyRequest)
                    .map(calendlyEventType -> {
                        // Save updated session
                        Session savedSession = sessionService.save(session);
                        return buildSessionEventTypeResponse(savedSession, calendlyEventType);
                    });
        })
        .map(response -> ResponseEntity.ok(response))
        .onErrorResume(error -> {
            log.error("Failed to update event type for session: {}", sessionId, error);
            return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build());
        });
    }

    /**
     * Delete EventType from Calendly and deactivate corresponding Session
     */
    @DeleteMapping("/event-types/{sessionId}")
    public Mono<ResponseEntity<Void>> deleteEventType(@PathVariable Long sessionId) {
        log.info("Deleting event type for session: {}", sessionId);

        return Mono.fromCallable(() -> {
            Optional<Session> sessionOpt = sessionService.findById(sessionId);
            if (sessionOpt.isEmpty()) {
                throw new RuntimeException("Session not found with ID: " + sessionId);
            }
            return sessionOpt.get();
        })
        .flatMap(session -> {
            if (session.getEventTypeUri() != null) {
                return calendlyService.deleteEventType(session.getEventTypeUri())
                        .then(Mono.fromRunnable(() -> sessionService.deleteSessionByEventTypeUri(session.getEventTypeUri())));
            } else {
                // Just deactivate the session if no Calendly event type
                session.setActive(false);
                sessionService.save(session);
                return Mono.empty();
            }
        })
        .then(Mono.just(ResponseEntity.noContent().<Void>build()))
        .onErrorResume(error -> {
            log.error("Failed to delete event type for session: {}", sessionId, error);
            return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build());
        });
    }

    /**
     * Get all EventTypes for a tutor
     */
    @GetMapping("/event-types/tutor/{tutorId}")
    public ResponseEntity<List<SessionEventTypeResponse>> getTutorEventTypes(@PathVariable Long tutorId) {
        log.info("Getting event types for tutor: {}", tutorId);

        try {
            ContactDetails tutor = getContactDetailsById(tutorId);
            List<Session> sessions = sessionService.findByTutor(tutor);

            List<SessionEventTypeResponse> responses = sessions.stream()
                    .map(session -> buildSessionEventTypeResponse(session, null))
                    .collect(Collectors.toList());

            return ResponseEntity.ok(responses);
        } catch (Exception e) {
            log.error("Failed to get event types for tutor: {}", tutorId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    // Helper methods
    private ContactDetails getContactDetailsById(Long id) {
        try {
            List<Map<String, Object>> data = dynamicEntityService.getEntitiesByField("ContactDetails", "id", id.toString());
            if (data.isEmpty()) {
                throw new RuntimeException("ContactDetails not found with ID: " + id);
            }
            // Convert map to entity - this is a simplified approach
            // In a real implementation, you might want to use a proper mapper
            return ContactDetails.builder()
                    .id(id)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to get ContactDetails with ID: " + id, e);
        }
    }

    private SessionType getSessionTypeById(Long id) {
        try {
            List<Map<String, Object>> data = dynamicEntityService.getEntitiesByField("SessionType", "id", id.toString());
            if (data.isEmpty()) {
                throw new RuntimeException("SessionType not found with ID: " + id);
            }
            return SessionType.builder()
                    .id(id)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to get SessionType with ID: " + id, e);
        }
    }

    private SubjectMaster getSubjectById(Long id) {
        try {
            List<Map<String, Object>> data = dynamicEntityService.getEntitiesByField("SubjectMaster", "id", id.toString());
            if (data.isEmpty()) {
                throw new RuntimeException("SubjectMaster not found with ID: " + id);
            }
            return SubjectMaster.builder()
                    .id(id)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to get SubjectMaster with ID: " + id, e);
        }
    }

    private SubjectSubCategory getSubjectSubCategoryById(Long id) {
        try {
            List<Map<String, Object>> data = dynamicEntityService.getEntitiesByField("SubjectSubCategory", "id", id.toString());
            if (data.isEmpty()) {
                throw new RuntimeException("SubjectSubCategory not found with ID: " + id);
            }
            return SubjectSubCategory.builder()
                    .id(id)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to get SubjectSubCategory with ID: " + id, e);
        }
    }

    private SessionLocation getSessionLocationById(Long id) {
        try {
            List<Map<String, Object>> data = dynamicEntityService.getEntitiesByField("SessionLocation", "id", id.toString());
            if (data.isEmpty()) {
                throw new RuntimeException("SessionLocation not found with ID: " + id);
            }
            return SessionLocation.builder()
                    .id(id)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to get SessionLocation with ID: " + id, e);
        }
    }

    private SessionEventTypeResponse buildSessionEventTypeResponse(Session session, CalendlyEventType calendlyEventType) {
        SessionEventTypeResponse.SessionEventTypeResponseBuilder builder = SessionEventTypeResponse.builder()
                .sessionId(session.getId())
                .title(session.getTitle())
                .description(session.getDescription())
                .startTime(session.getStartTime())
                .endTime(session.getEndTime())
                .sessionPrice(session.getSessionPrice())
                .currencyCode(session.getCurrencyCode())
                .minStudents(session.getMinStudents())
                .maxStudents(session.getMaxStudents())
                .active(session.isActive())
                .tutorId(session.getTutorId().getId())
                .tutorName(session.getTutorId().getDisplayName())
                .sessionTypeId(session.getSessionType().getId())
                .subjectId(session.getSubject().getId())
                .sessionLocationId(session.getSessionLocation().getId())
                .eventTypeUri(session.getEventTypeUri())
                .ownerUri(session.getOwnerUri())
                .schedulingUrl(session.getSchedulingUrl());

        // Add Calendly-specific data if available
        if (calendlyEventType != null) {
            builder.duration(calendlyEventType.getDuration())
                    .calendlyCreatedAt(calendlyEventType.getCreatedAt())
                    .calendlyUpdatedAt(calendlyEventType.getUpdatedAt())
                    .color(calendlyEventType.getColor())
                    .kind(calendlyEventType.getKind())
                    .poolingType(calendlyEventType.getPoolingType())
                    .type(calendlyEventType.getType())
                    .bookingMethod(calendlyEventType.getBookingMethod());
        }

        // Add subject sub-category if available
        if (session.getSubjectSubCategory() != null) {
            builder.subjectSubCategoryId(session.getSubjectSubCategory().getId());
        }

        return builder.build();
    }

    // ===== ORGANIZATION INVITE ENDPOINTS =====

    /**
     * Create organization invitation for a user
     */
    @PostMapping("/organization-invites")
    public Mono<ResponseEntity<Map<String, Object>>> createOrganizationInvite(@RequestBody Map<String, Object> request) {
        log.info("Creating organization invitation for user: {}", request.get("contactId"));

        return Mono.fromCallable(() -> {
            Long contactId = Long.valueOf(request.get("contactId").toString());
            String email = request.get("email").toString();

            // Get contact details
            ContactDetails contact = getContactDetailsById(contactId);

            // Check if there's already a pending invitation
            if (organizationInviteRepository.existsByContactAndStatus(contact, "PENDING")) {
                throw new RuntimeException("User already has a pending organization invitation");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("contact", contact);
            result.put("email", email);
            return result;
        })
        .flatMap(data -> {
            ContactDetails contact = (ContactDetails) data.get("contact");
            String email = (String) data.get("email");

            // Create Calendly organization invitation
            CreateOrganizationInvitationRequest calendlyRequest = CreateOrganizationInvitationRequest.builder()
                    .email(email)
                    .build();

            return calendlyService.createOrganizationInvitation(calendlyRequest)
                    .map(calendlyInvitation -> {
                        // Create local organization invite record
                        StatusMaster statusMaster = getStatusMasterByName("ACTIVE");

                        OrganizationInvite orgInvite = OrganizationInvite.builder()
                                .contact(contact)
                                .email(email)
                                .calendlyInvitationUri(calendlyInvitation.getUri())
                                .status("PENDING")
                                .invitedAt(java.time.LocalDateTime.now())
                                .lastSentAt(calendlyInvitation.getLastSentAt() != null ?
                                          calendlyInvitation.getLastSentAt().toLocalDateTime() :
                                          java.time.LocalDateTime.now())
                                .hasCalendarPermission(false)
                                .statusMaster(statusMaster)
                                .build();

                        OrganizationInvite savedInvite = organizationInviteRepository.save(orgInvite);

                        Map<String, Object> response = new HashMap<>();
                        response.put("inviteId", savedInvite.getId());
                        response.put("calendlyInvitationUri", calendlyInvitation.getUri());
                        response.put("email", email);
                        response.put("status", "PENDING");
                        response.put("invitedAt", savedInvite.getInvitedAt());
                        response.put("message", "Organization invitation sent successfully");
                        return response;
                    });
        })
        .map(response -> ResponseEntity.status(HttpStatus.CREATED).body(response))
        .onErrorResume(error -> {
            log.error("Failed to create organization invitation", error);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", error.getMessage());
            return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(errorResponse));
        });
    }

    /**
     * Get organization invitation status
     */
    @GetMapping("/organization-invites/{inviteId}")
    public Mono<ResponseEntity<Map<String, Object>>> getOrganizationInvite(@PathVariable Long inviteId) {
        log.info("Getting organization invitation: {}", inviteId);

        return Mono.fromCallable(() -> {
            Optional<OrganizationInvite> inviteOpt = organizationInviteRepository.findById(inviteId);
            if (inviteOpt.isEmpty()) {
                throw new RuntimeException("Organization invitation not found with ID: " + inviteId);
            }
            return inviteOpt.get();
        })
        .flatMap(invite -> {
            if (invite.getCalendlyInvitationUri() != null) {
                return calendlyService.getOrganizationInvitation(invite.getCalendlyInvitationUri())
                        .map(calendlyInvitation -> {
                            // Update local status if needed
                            if (!invite.getStatus().equals(calendlyInvitation.getStatus())) {
                                invite.setStatus(calendlyInvitation.getStatus());
                                if ("ACCEPTED".equals(calendlyInvitation.getStatus())) {
                                    invite.setAcceptedAt(java.time.LocalDateTime.now());
                                    invite.setCalendlyUserUri(calendlyInvitation.getUser());
                                }
                                organizationInviteRepository.save(invite);
                            }

                            Map<String, Object> response = new HashMap<>();
                            response.put("inviteId", invite.getId());
                            response.put("email", invite.getEmail());
                            response.put("status", invite.getStatus());
                            response.put("invitedAt", invite.getInvitedAt());
                            response.put("acceptedAt", invite.getAcceptedAt());
                            response.put("calendlyUserUri", invite.getCalendlyUserUri());
                            response.put("hasCalendarPermission", invite.getHasCalendarPermission());
                            response.put("calendlyInvitationUri", invite.getCalendlyInvitationUri());
                            return response;
                        });
            } else {
                Map<String, Object> response = new HashMap<>();
                response.put("inviteId", invite.getId());
                response.put("email", invite.getEmail());
                response.put("status", invite.getStatus());
                response.put("invitedAt", invite.getInvitedAt());
                response.put("message", "No Calendly invitation URI found");
                return Mono.just(response);
            }
        })
        .map(response -> ResponseEntity.ok(response))
        .onErrorResume(error -> {
            log.error("Failed to get organization invitation: {}", inviteId, error);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", error.getMessage());
            return Mono.just(ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(errorResponse));
        });
    }

    // ===== ENHANCED REGISTRATION ENDPOINTS =====

    /**
     * Enhanced user registration with Calendly organization invite
     */
    @PostMapping("/register-with-calendly")
    public Mono<ResponseEntity<Map<String, Object>>> registerWithCalendly(@Valid @RequestBody SignupRequest signupRequest) {
        log.info("Enhanced registration request for user: {}", signupRequest.getUsername());
        return enhancedRegistrationService.registerUserWithCalendlyInvite(signupRequest);
    }

    /**
     * Check registration status and update if Calendly invite was accepted
     */
    @GetMapping("/registration-status/{userId}")
    public Mono<ResponseEntity<Map<String, Object>>> checkRegistrationStatus(@PathVariable Long userId) {
        log.info("Checking registration status for user: {}", userId);
        return enhancedRegistrationService.checkRegistrationStatus(userId);
    }

    /**
     * Resend Calendly organization invitation
     */
    @PostMapping("/resend-calendly-invite/{userId}")
    public Mono<ResponseEntity<Map<String, Object>>> resendCalendlyInvite(@PathVariable Long userId) {
        log.info("Resending Calendly invite for user: {}", userId);
        return enhancedRegistrationService.resendCalendlyInvite(userId);
    }

    /**
     * Webhook endpoint to handle Calendly organization invitation events
     */
    @PostMapping("/webhooks/calendly-invite")
    public ResponseEntity<Map<String, Object>> handleCalendlyInviteWebhook(@RequestBody Map<String, Object> webhookPayload) {
        log.info("Received Calendly webhook: {}", webhookPayload);

        try {
            String eventType = (String) webhookPayload.get("event");

            if ("invitee.created".equals(eventType) || "organization_membership.created".equals(eventType)) {
                // Extract invitation URI and user email from webhook payload
                Map<String, Object> payload = (Map<String, Object>) webhookPayload.get("payload");
                String invitationUri = (String) payload.get("uri");
                // String userEmail = (String) payload.get("email"); // Available if needed for validation

                // Find the organization invite by URI and update status
                Optional<OrganizationInvite> inviteOpt = organizationInviteRepository.findByCalendlyInvitationUri(invitationUri);
                if (inviteOpt.isPresent()) {
                    OrganizationInvite invite = inviteOpt.get();
                    invite.setStatus("ACCEPTED");
                    invite.setAcceptedAt(java.time.LocalDateTime.now());
                    invite.setHasCalendarPermission(true);
                    organizationInviteRepository.save(invite);

                    // Complete user registration
                    enhancedRegistrationService.completeUserRegistration(invite.getContact().getId());

                    Map<String, Object> response = new HashMap<>();
                    response.put("status", "success");
                    response.put("message", "User registration completed via webhook");
                    response.put("userId", invite.getContact().getId());

                    return ResponseEntity.ok(response);
                }
            }

            Map<String, Object> response = new HashMap<>();
            response.put("status", "ignored");
            response.put("message", "Webhook event not processed");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to process Calendly webhook", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", "error");
            errorResponse.put("message", "Failed to process webhook: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    // ===== AVAILABILITY OVERRIDE ENDPOINTS =====

    /**
     * Create availability override for a tutor
     */
    @PostMapping("/availability-overrides")
    public ResponseEntity<AvailabilityOverrideResponse> createAvailabilityOverride(
            @Valid @RequestBody AvailabilityOverrideRequest request) {
        log.info("Creating availability override for tutor: {} on date: {}",
                request.getTutorId(), request.getOverrideDate());

        try {
            ContactDetails tutor = getContactDetailsById(request.getTutorId());

            TutorAvailabilityOverride override = tutorAvailabilityService.createOrUpdateAvailabilityOverride(
                    tutor,
                    request.getOverrideDate(),
                    request.getStartTime(),
                    request.getEndTime(),
                    request.getIsAvailable());

            AvailabilityOverrideResponse response = buildAvailabilityOverrideResponse(override);
            return ResponseEntity.status(201).body(response);

        } catch (Exception e) {
            log.error("Failed to create availability override", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * Get availability overrides for a tutor
     */
    @GetMapping("/availability-overrides/tutor/{tutorId}")
    public ResponseEntity<List<AvailabilityOverrideResponse>> getTutorAvailabilityOverrides(
            @PathVariable Long tutorId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        log.info("Getting availability overrides for tutor: {}", tutorId);

        try {
            ContactDetails tutor = getContactDetailsById(tutorId);
            List<TutorAvailabilityOverride> overrides;

            if (startDate != null && endDate != null) {
                java.time.LocalDate start = java.time.LocalDate.parse(startDate);
                java.time.LocalDate end = java.time.LocalDate.parse(endDate);
                overrides = tutorAvailabilityService.getTutorAvailabilityOverrides(tutor, start, end);
            } else {
                overrides = tutorAvailabilityService.getTutorAvailabilityOverrides(tutor);
            }

            List<AvailabilityOverrideResponse> responses = overrides.stream()
                    .map(this::buildAvailabilityOverrideResponse)
                    .collect(java.util.stream.Collectors.toList());

            return ResponseEntity.ok(responses);

        } catch (Exception e) {
            log.error("Failed to get availability overrides for tutor: {}", tutorId, e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * Update availability override
     */
    @PutMapping("/availability-overrides/{overrideId}")
    public ResponseEntity<AvailabilityOverrideResponse> updateAvailabilityOverride(
            @PathVariable Long overrideId,
            @RequestParam Long tutorId,
            @Valid @RequestBody AvailabilityOverrideRequest request) {
        log.info("Updating availability override: {} for tutor: {}", overrideId, tutorId);

        try {
            ContactDetails tutor = getContactDetailsById(tutorId);

            Optional<TutorAvailabilityOverride> updatedOverride =
                    tutorAvailabilityService.updateAvailabilityOverride(
                            overrideId,
                            tutor,
                            request.getStartTime(),
                            request.getEndTime(),
                            request.getIsAvailable());

            if (updatedOverride.isPresent()) {
                AvailabilityOverrideResponse response = buildAvailabilityOverrideResponse(updatedOverride.get());
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            log.error("Failed to update availability override: {}", overrideId, e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * Delete availability override
     */
    @DeleteMapping("/availability-overrides/{overrideId}")
    public ResponseEntity<Void> deleteAvailabilityOverride(
            @PathVariable Long overrideId,
            @RequestParam Long tutorId) {
        log.info("Deleting availability override: {} for tutor: {}", overrideId, tutorId);

        try {
            ContactDetails tutor = getContactDetailsById(tutorId);
            boolean deleted = tutorAvailabilityService.deleteAvailabilityOverride(overrideId, tutor);

            if (deleted) {
                return ResponseEntity.noContent().build();
            } else {
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            log.error("Failed to delete availability override: {}", overrideId, e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * Check if tutor is available at a specific time
     */
    @GetMapping("/availability-check/tutor/{tutorId}")
    public ResponseEntity<Map<String, Object>> checkTutorAvailability(
            @PathVariable Long tutorId,
            @RequestParam String dateTime) {
        log.info("Checking availability for tutor: {} at time: {}", tutorId, dateTime);

        try {
            ContactDetails tutor = getContactDetailsById(tutorId);
            java.time.LocalDateTime requestedDateTime = java.time.LocalDateTime.parse(dateTime);

            boolean isAvailable = tutorAvailabilityService.isTutorAvailable(tutor, requestedDateTime);

            Map<String, Object> response = new HashMap<>();
            response.put("tutorId", tutorId);
            response.put("dateTime", dateTime);
            response.put("isAvailable", isAvailable);
            response.put("message", isAvailable ? "Tutor is available" : "Tutor is not available due to override");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to check tutor availability", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    // ===== TUTOR AVAILABILITY ENDPOINTS =====

    /**
     * Get tutor availability for a specific date range
     */
    @GetMapping("/availability/tutor/{tutorId}")
    public Mono<ResponseEntity<Map<String, Object>>> getTutorAvailability(
            @PathVariable Long tutorId,
            @RequestParam String startDate,
            @RequestParam String endDate) {
        log.info("Getting availability for tutor: {} from {} to {}", tutorId, startDate, endDate);

        return Mono.fromCallable(() -> {
            ContactDetails tutor = getContactDetailsById(tutorId);
            java.time.LocalDate start = java.time.LocalDate.parse(startDate);
            java.time.LocalDate end = java.time.LocalDate.parse(endDate);

            // Get local availability overrides
            List<TutorAvailabilityOverride> overrides =
                    tutorAvailabilityService.getTutorAvailabilityOverrides(tutor, start, end);

            Map<String, Object> result = new HashMap<>();
            result.put("tutorId", tutorId);
            result.put("startDate", startDate);
            result.put("endDate", endDate);
            result.put("overrides", overrides);

            return result;
        })
        .flatMap(localData -> {
            ContactDetails tutor = getContactDetailsById((Long) localData.get("tutorId"));

            // Get Calendly user URI for the tutor
            if (tutor.getEmailId() != null) {
                // Try to get Calendly availability
                return calendlyService.getCurrentUser()
                        .flatMap(currentUser -> {
                            java.time.LocalDate start = java.time.LocalDate.parse((String) localData.get("startDate"));
                            java.time.LocalDate end = java.time.LocalDate.parse((String) localData.get("endDate"));

                            return calendlyService.getUserAvailability(currentUser.getUri(), start, end)
                                    .map(calendlyAvailability -> {
                                        localData.put("calendlyAvailability", calendlyAvailability);
                                        return localData;
                                    });
                        })
                        .onErrorResume(error -> {
                            log.warn("Failed to get Calendly availability for tutor: {}", tutorId, error);
                            localData.put("calendlyError", error.getMessage());
                            return Mono.just(localData);
                        });
            } else {
                localData.put("calendlyError", "No email found for tutor");
                return Mono.just(localData);
            }
        })
        .map(data -> {
            Map<String, Object> response = new HashMap<>();
            response.put("tutorId", data.get("tutorId"));
            response.put("startDate", data.get("startDate"));
            response.put("endDate", data.get("endDate"));
            response.put("localOverrides", data.get("overrides"));

            if (data.containsKey("calendlyAvailability")) {
                response.put("calendlyAvailability", data.get("calendlyAvailability"));
            }

            if (data.containsKey("calendlyError")) {
                response.put("calendlyError", data.get("calendlyError"));
            }

            return ResponseEntity.ok(response);
        })
        .onErrorResume(error -> {
            log.error("Failed to get tutor availability", error);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", error.getMessage());
            return Mono.just(ResponseEntity.status(500).body(errorResponse));
        });
    }

    /**
     * Get tutor availability for a specific date and time
     */
    @GetMapping("/availability/tutor/{tutorId}/check")
    public ResponseEntity<Map<String, Object>> checkSpecificAvailability(
            @PathVariable Long tutorId,
            @RequestParam String dateTime) {
        log.info("Checking specific availability for tutor: {} at {}", tutorId, dateTime);

        try {
            ContactDetails tutor = getContactDetailsById(tutorId);
            java.time.LocalDateTime requestedDateTime = java.time.LocalDateTime.parse(dateTime);

            // Check local availability overrides
            boolean isLocallyAvailable = tutorAvailabilityService.isTutorAvailable(tutor, requestedDateTime);

            // Get any overrides for this specific date
            java.time.LocalDate date = requestedDateTime.toLocalDate();
            List<TutorAvailabilityOverride> dayOverrides =
                    tutorAvailabilityService.getTutorAvailabilityOverrides(tutor, date);

            Map<String, Object> response = new HashMap<>();
            response.put("tutorId", tutorId);
            response.put("dateTime", dateTime);
            response.put("isLocallyAvailable", isLocallyAvailable);
            response.put("dayOverrides", dayOverrides);

            // Determine final availability status
            boolean finalAvailability = isLocallyAvailable;
            String availabilityReason = "No overrides found";

            // Check if there are any overrides affecting this time
            for (TutorAvailabilityOverride override : dayOverrides) {
                if (requestedDateTime.isAfter(override.getStartTime()) &&
                    requestedDateTime.isBefore(override.getEndTime())) {
                    finalAvailability = override.getIsAvailable();
                    availabilityReason = "Override found: " + (override.getIsAvailable() ? "Available" : "Not Available");
                    break;
                }
            }

            response.put("isAvailable", finalAvailability);
            response.put("reason", availabilityReason);
            response.put("message", finalAvailability ?
                    "Tutor is available at the requested time" :
                    "Tutor is not available at the requested time");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to check specific availability for tutor: {}", tutorId, e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * Get available time slots for a tutor on a specific date
     */
    @GetMapping("/availability/tutor/{tutorId}/slots")
    public ResponseEntity<Map<String, Object>> getAvailableTimeSlots(
            @PathVariable Long tutorId,
            @RequestParam String date,
            @RequestParam(defaultValue = "60") int slotDurationMinutes) {
        log.info("Getting available time slots for tutor: {} on date: {}", tutorId, date);

        try {
            ContactDetails tutor = getContactDetailsById(tutorId);
            java.time.LocalDate requestedDate = java.time.LocalDate.parse(date);

            // Get overrides for this date
            List<TutorAvailabilityOverride> overrides =
                    tutorAvailabilityService.getTutorAvailabilityOverrides(tutor, requestedDate);

            // Generate time slots (example: 9 AM to 5 PM in specified intervals)
            List<Map<String, Object>> availableSlots = new java.util.ArrayList<>();
            java.time.LocalDateTime startOfDay = requestedDate.atTime(9, 0); // 9 AM
            java.time.LocalDateTime endOfDay = requestedDate.atTime(17, 0);   // 5 PM

            java.time.LocalDateTime currentSlot = startOfDay;
            while (currentSlot.plusMinutes(slotDurationMinutes).isBefore(endOfDay) ||
                   currentSlot.plusMinutes(slotDurationMinutes).equals(endOfDay)) {

                java.time.LocalDateTime slotEnd = currentSlot.plusMinutes(slotDurationMinutes);
                boolean isSlotAvailable = true;
                String unavailableReason = null;

                // Check if this slot conflicts with any override
                for (TutorAvailabilityOverride override : overrides) {
                    if ((currentSlot.isAfter(override.getStartTime()) && currentSlot.isBefore(override.getEndTime())) ||
                        (slotEnd.isAfter(override.getStartTime()) && slotEnd.isBefore(override.getEndTime())) ||
                        (currentSlot.isBefore(override.getStartTime()) && slotEnd.isAfter(override.getEndTime()))) {

                        isSlotAvailable = override.getIsAvailable();
                        if (!isSlotAvailable) {
                            unavailableReason = "Blocked by availability override";
                        }
                        break;
                    }
                }

                Map<String, Object> slot = new HashMap<>();
                slot.put("startTime", currentSlot.toString());
                slot.put("endTime", slotEnd.toString());
                slot.put("isAvailable", isSlotAvailable);
                if (unavailableReason != null) {
                    slot.put("reason", unavailableReason);
                }

                availableSlots.add(slot);
                currentSlot = currentSlot.plusMinutes(slotDurationMinutes);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("tutorId", tutorId);
            response.put("date", date);
            response.put("slotDurationMinutes", slotDurationMinutes);
            response.put("totalSlots", availableSlots.size());
            response.put("availableSlots", availableSlots.stream()
                    .filter(slot -> (Boolean) slot.get("isAvailable"))
                    .collect(java.util.stream.Collectors.toList()));
            response.put("unavailableSlots", availableSlots.stream()
                    .filter(slot -> !(Boolean) slot.get("isAvailable"))
                    .collect(java.util.stream.Collectors.toList()));
            response.put("allSlots", availableSlots);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to get available time slots for tutor: {}", tutorId, e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    private AvailabilityOverrideResponse buildAvailabilityOverrideResponse(TutorAvailabilityOverride override) {
        return AvailabilityOverrideResponse.builder()
                .id(override.getId())
                .tutorId(override.getContact().getId())
                .tutorName(override.getContact().getDisplayName())
                .overrideDate(tutorAvailabilityService.epochDayToLocalDate(override.getOverrideDate()))
                .startTime(override.getStartTime())
                .endTime(override.getEndTime())
                .isAvailable(override.getIsAvailable())
                .build();
    }

    private StatusMaster getStatusMasterByName(String statusName) {
        try {
            List<Map<String, Object>> data = dynamicEntityService.getEntitiesByField("StatusMaster", "statusName", statusName);
            if (data.isEmpty()) {
                throw new RuntimeException("StatusMaster not found with name: " + statusName);
            }
            return StatusMaster.builder()
                    .id(Long.valueOf(data.get(0).get("id").toString()))
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to get StatusMaster with name: " + statusName, e);
        }
    }
}
