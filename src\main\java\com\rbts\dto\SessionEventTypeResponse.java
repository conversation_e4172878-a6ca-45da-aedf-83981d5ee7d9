package com.rbts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionEventTypeResponse {
    
    private Long sessionId;
    private String title;
    private String description;
    private Integer duration;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private BigDecimal sessionPrice;
    private String currencyCode;
    private Long minStudents;
    private Long maxStudents;
    private boolean active;
    
    // Tutor information
    private Long tutorId;
    private String tutorName;
    private String tutorEmail;
    
    // Session type and subject information
    private Long sessionTypeId;
    private String sessionTypeName;
    private Long subjectId;
    private String subjectName;
    private Long subjectSubCategoryId;
    private String subjectSubCategoryName;
    
    // Location information
    private Long sessionLocationId;
    private String sessionLocationName;
    
    // Calendly integration data
    @JsonProperty("event_type_uri")
    private String eventTypeUri;
    
    @JsonProperty("owner_uri")
    private String ownerUri;
    
    @JsonProperty("scheduling_url")
    private String schedulingUrl;
    
    @JsonProperty("calendly_created_at")
    private OffsetDateTime calendlyCreatedAt;
    
    @JsonProperty("calendly_updated_at")
    private OffsetDateTime calendlyUpdatedAt;
    
    private String color;
    private String kind;
    
    @JsonProperty("pooling_type")
    private String poolingType;
    
    private String type;
    
    @JsonProperty("booking_method")
    private String bookingMethod;
}
