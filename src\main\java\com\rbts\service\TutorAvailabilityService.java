package com.rbts.service;

import com.rbts.entity.ContactDetails;
import com.rbts.entity.TutorAvailabilityOverride;
import com.rbts.repository.TutorAvailabilityOverrideRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class TutorAvailabilityService {

    private final TutorAvailabilityOverrideRepository availabilityOverrideRepository;

    /**
     * Create or update availability override for a tutor
     */
    @Transactional
    public TutorAvailabilityOverride createOrUpdateAvailabilityOverride(
            ContactDetails tutor,
            LocalDate overrideDate,
            LocalDateTime startTime,
            LocalDateTime endTime,
            Boolean isAvailable) {
        
        log.info("Creating/updating availability override for tutor: {} on date: {}", 
                tutor.getId(), overrideDate);
        
        // Convert LocalDate to Long (epoch day)
        Long overrideDateLong = overrideDate.toEpochDay();
        
        // Check if override already exists for this tutor, date, and start time
        Optional<TutorAvailabilityOverride> existingOverride = 
                availabilityOverrideRepository.findByContactAndOverrideDateAndStartTime(
                        tutor, overrideDateLong, startTime);
        
        TutorAvailabilityOverride override;
        if (existingOverride.isPresent()) {
            // Update existing override
            override = existingOverride.get();
            override.setEndTime(endTime);
            override.setIsAvailable(isAvailable);
            log.info("Updated existing availability override with ID: {}", override.getId());
        } else {
            // Create new override
            override = TutorAvailabilityOverride.builder()
                    .contact(tutor)
                    .overrideDate(overrideDateLong)
                    .startTime(startTime)
                    .endTime(endTime)
                    .isAvailable(isAvailable)
                    .build();
            log.info("Created new availability override for tutor: {}", tutor.getId());
        }
        
        return availabilityOverrideRepository.save(override);
    }

    /**
     * Get all availability overrides for a tutor
     */
    public List<TutorAvailabilityOverride> getTutorAvailabilityOverrides(ContactDetails tutor) {
        log.info("Getting availability overrides for tutor: {}", tutor.getId());
        return availabilityOverrideRepository.findByContact(tutor);
    }

    /**
     * Get availability overrides for a tutor within a date range
     */
    public List<TutorAvailabilityOverride> getTutorAvailabilityOverrides(
            ContactDetails tutor, 
            LocalDate startDate, 
            LocalDate endDate) {
        
        log.info("Getting availability overrides for tutor: {} from {} to {}", 
                tutor.getId(), startDate, endDate);
        
        Long startDateLong = startDate.toEpochDay();
        Long endDateLong = endDate.toEpochDay();
        
        return availabilityOverrideRepository.findByContactAndOverrideDateBetween(
                tutor, startDateLong, endDateLong);
    }

    /**
     * Get availability overrides for a specific date
     */
    public List<TutorAvailabilityOverride> getTutorAvailabilityOverrides(
            ContactDetails tutor, 
            LocalDate date) {
        
        log.info("Getting availability overrides for tutor: {} on date: {}", 
                tutor.getId(), date);
        
        Long dateLong = date.toEpochDay();
        return availabilityOverrideRepository.findByContactAndOverrideDate(tutor, dateLong);
    }

    /**
     * Delete availability override
     */
    @Transactional
    public boolean deleteAvailabilityOverride(Long overrideId, ContactDetails tutor) {
        log.info("Deleting availability override: {} for tutor: {}", overrideId, tutor.getId());
        
        Optional<TutorAvailabilityOverride> overrideOpt = availabilityOverrideRepository.findById(overrideId);
        if (overrideOpt.isPresent()) {
            TutorAvailabilityOverride override = overrideOpt.get();
            
            // Verify the override belongs to the tutor
            if (!override.getContact().getId().equals(tutor.getId())) {
                log.warn("Attempted to delete override {} that doesn't belong to tutor {}", 
                        overrideId, tutor.getId());
                return false;
            }
            
            // Since there's no isActive field, we'll do a hard delete
            availabilityOverrideRepository.delete(override);
            // Already deleted above
            
            log.info("Successfully deleted availability override: {}", overrideId);
            return true;
        }
        
        log.warn("Availability override not found: {}", overrideId);
        return false;
    }

    /**
     * Check if tutor is available at a specific time (considering overrides)
     */
    public boolean isTutorAvailable(ContactDetails tutor, LocalDateTime dateTime) {
        LocalDate date = dateTime.toLocalDate();
        Long dateLong = date.toEpochDay();
        
        List<TutorAvailabilityOverride> overrides =
                availabilityOverrideRepository.findByContactAndOverrideDate(tutor, dateLong);
        
        // Check if the requested time falls within any override period
        for (TutorAvailabilityOverride override : overrides) {
            if (dateTime.isAfter(override.getStartTime()) && dateTime.isBefore(override.getEndTime())) {
                log.info("Tutor {} availability at {} is overridden (isAvailable: {})",
                        tutor.getId(), dateTime, override.getIsAvailable());
                return override.getIsAvailable(); // Return the override availability status
            }
        }
        
        return true;
    }

    /**
     * Get all overrides for multiple tutors within a date range
     */
    public List<TutorAvailabilityOverride> getAvailabilityOverrides(
            List<ContactDetails> tutors, 
            LocalDate startDate, 
            LocalDate endDate) {
        
        log.info("Getting availability overrides for {} tutors from {} to {}", 
                tutors.size(), startDate, endDate);
        
        Long startDateLong = startDate.toEpochDay();
        Long endDateLong = endDate.toEpochDay();
        
        return availabilityOverrideRepository.findByContactInAndOverrideDateBetweenAndIsActiveTrue(
                tutors, startDateLong, endDateLong);
    }

    /**
     * Update availability override
     */
    @Transactional
    public Optional<TutorAvailabilityOverride> updateAvailabilityOverride(
            Long overrideId,
            ContactDetails tutor,
            LocalDateTime startTime,
            LocalDateTime endTime,
            Boolean isAvailable) {
        
        log.info("Updating availability override: {} for tutor: {}", overrideId, tutor.getId());
        
        Optional<TutorAvailabilityOverride> overrideOpt = availabilityOverrideRepository.findById(overrideId);
        if (overrideOpt.isPresent()) {
            TutorAvailabilityOverride override = overrideOpt.get();
            
            // Verify the override belongs to the tutor
            if (!override.getContact().getId().equals(tutor.getId())) {
                log.warn("Attempted to update override {} that doesn't belong to tutor {}", 
                        overrideId, tutor.getId());
                return Optional.empty();
            }
            
            override.setStartTime(startTime);
            override.setEndTime(endTime);
            override.setIsAvailable(isAvailable);
            
            TutorAvailabilityOverride savedOverride = availabilityOverrideRepository.save(override);
            log.info("Successfully updated availability override: {}", overrideId);
            
            return Optional.of(savedOverride);
        }
        
        log.warn("Availability override not found: {}", overrideId);
        return Optional.empty();
    }

    /**
     * Get availability override by ID
     */
    public Optional<TutorAvailabilityOverride> getAvailabilityOverride(Long overrideId) {
        return availabilityOverrideRepository.findById(overrideId);
    }

    /**
     * Convert epoch day back to LocalDate for display purposes
     */
    public LocalDate epochDayToLocalDate(Long epochDay) {
        return LocalDate.ofEpochDay(epochDay);
    }
}
