package com.rbts.repository;

import com.rbts.entity.ContactDetails;
import com.rbts.entity.Session;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface SessionRepository extends JpaRepository<Session, Long> {

    Optional<Session> findByEventTypeUri(String eventTypeUri);
    
    List<Session> findByTutorId(ContactDetails tutorId);
    
    List<Session> findByTutorIdAndActive(ContactDetails tutorId, boolean active);
    
    List<Session> findByActive(boolean active);
    
    @Query("SELECT s FROM Session s WHERE s.tutorId = :tutorId AND s.startTime >= :startTime AND s.endTime <= :endTime")
    List<Session> findByTutorIdAndTimeRange(@Param("tutorId") ContactDetails tutorId, 
                                           @Param("startTime") LocalDateTime startTime, 
                                           @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT s FROM Session s WHERE s.tutorId = :tutorId AND s.active = true AND s.startTime >= :currentTime")
    List<Session> findActiveFutureSessionsByTutor(@Param("tutorId") ContactDetails tutorId, 
                                                 @Param("currentTime") LocalDateTime currentTime);
    
    boolean existsByEventTypeUri(String eventTypeUri);
    
    @Query("SELECT COUNT(s) FROM Session s WHERE s.tutorId = :tutorId AND s.active = true")
    Long countActiveSessionsByTutor(@Param("tutorId") ContactDetails tutorId);
}
