package com.rbts.service.calendly;

import com.rbts.config.CalendlyProperties;
import com.rbts.dto.calendly.*;
import com.rbts.dto.calendly.request.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class CalendlyService {

    @Qualifier("calendlyWebClient")
    private final WebClient webClient;
    
    private final CalendlyProperties calendlyProperties;

    // EventType CRUD Operations
    public Mono<CalendlyEventType> createEventType(CreateEventTypeRequest request) {
        log.info("Creating Calendly event type: {}", request.getName());
        
        return webClient.post()
                .uri(calendlyProperties.getEndpoints().getEventTypes())
                .bodyValue(request)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<CalendlyApiResponse<CalendlyEventType>>() {})
                .map(CalendlyApiResponse::getResource)
                .doOnSuccess(eventType -> log.info("Created event type with URI: {}", eventType.getUri()))
                .doOnError(error -> log.error("Failed to create event type", error));
    }

    public Mono<CalendlyEventType> getEventType(String eventTypeUri) {
        log.info("Fetching Calendly event type: {}", eventTypeUri);
        
        return webClient.get()
                .uri(eventTypeUri)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<CalendlyApiResponse<CalendlyEventType>>() {})
                .map(CalendlyApiResponse::getResource)
                .doOnSuccess(eventType -> log.info("Retrieved event type: {}", eventType.getName()))
                .doOnError(error -> log.error("Failed to get event type: {}", eventTypeUri, error));
    }

    public Mono<CalendlyEventType> updateEventType(String eventTypeUri, UpdateEventTypeRequest request) {
        log.info("Updating Calendly event type: {}", eventTypeUri);
        
        return webClient.patch()
                .uri(eventTypeUri)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<CalendlyApiResponse<CalendlyEventType>>() {})
                .map(CalendlyApiResponse::getResource)
                .doOnSuccess(eventType -> log.info("Updated event type: {}", eventType.getName()))
                .doOnError(error -> log.error("Failed to update event type: {}", eventTypeUri, error));
    }

    public Mono<Void> deleteEventType(String eventTypeUri) {
        log.info("Deleting Calendly event type: {}", eventTypeUri);
        
        return webClient.delete()
                .uri(eventTypeUri)
                .retrieve()
                .bodyToMono(Void.class)
                .doOnSuccess(result -> log.info("Deleted event type: {}", eventTypeUri))
                .doOnError(error -> log.error("Failed to delete event type: {}", eventTypeUri, error));
    }

    public Mono<List<CalendlyEventType>> getUserEventTypes(String userUri) {
        log.info("Fetching event types for user: {}", userUri);
        
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path(calendlyProperties.getEndpoints().getEventTypes())
                        .queryParam("user", userUri)
                        .queryParam("active", true)
                        .build())
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<CalendlyApiResponse<CalendlyEventType>>() {})
                .map(CalendlyApiResponse::getCollection)
                .doOnSuccess(eventTypes -> log.info("Retrieved {} event types for user", eventTypes.size()))
                .doOnError(error -> log.error("Failed to get event types for user: {}", userUri, error));
    }

    // Organization Invitation Operations
    public Mono<CalendlyOrganizationInvitation> createOrganizationInvitation(CreateOrganizationInvitationRequest request) {
        log.info("Creating organization invitation for email: {}", request.getEmail());
        
        return webClient.post()
                .uri(uriBuilder -> uriBuilder
                        .path(calendlyProperties.getEndpoints().getOrganizationInvitations())
                        .queryParam("organization", calendlyProperties.getOrganizationUri())
                        .build())
                .bodyValue(request)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<CalendlyApiResponse<CalendlyOrganizationInvitation>>() {})
                .map(CalendlyApiResponse::getResource)
                .doOnSuccess(invitation -> log.info("Created organization invitation with URI: {}", invitation.getUri()))
                .doOnError(error -> log.error("Failed to create organization invitation", error));
    }

    public Mono<CalendlyOrganizationInvitation> getOrganizationInvitation(String invitationUri) {
        log.info("Fetching organization invitation: {}", invitationUri);
        
        return webClient.get()
                .uri(invitationUri)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<CalendlyApiResponse<CalendlyOrganizationInvitation>>() {})
                .map(CalendlyApiResponse::getResource)
                .doOnSuccess(invitation -> log.info("Retrieved organization invitation for: {}", invitation.getEmail()))
                .doOnError(error -> log.error("Failed to get organization invitation: {}", invitationUri, error));
    }

    public Mono<Void> revokeOrganizationInvitation(String invitationUri) {
        log.info("Revoking organization invitation: {}", invitationUri);
        
        return webClient.delete()
                .uri(invitationUri)
                .retrieve()
                .bodyToMono(Void.class)
                .doOnSuccess(result -> log.info("Revoked organization invitation: {}", invitationUri))
                .doOnError(error -> log.error("Failed to revoke organization invitation: {}", invitationUri, error));
    }

    // User Operations
    public Mono<CalendlyUser> getCurrentUser() {
        log.info("Fetching current user information");
        
        return webClient.get()
                .uri(calendlyProperties.getEndpoints().getUsers() + "/me")
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<CalendlyApiResponse<CalendlyUser>>() {})
                .map(CalendlyApiResponse::getResource)
                .doOnSuccess(user -> log.info("Retrieved current user: {}", user.getName()))
                .doOnError(error -> log.error("Failed to get current user", error));
    }

    public Mono<CalendlyUser> getUser(String userUri) {
        log.info("Fetching user: {}", userUri);
        
        return webClient.get()
                .uri(userUri)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<CalendlyApiResponse<CalendlyUser>>() {})
                .map(CalendlyApiResponse::getResource)
                .doOnSuccess(user -> log.info("Retrieved user: {}", user.getName()))
                .doOnError(error -> log.error("Failed to get user: {}", userUri, error));
    }

    // Availability Operations
    public Mono<CalendlyAvailability> getUserAvailability(String userUri, LocalDate startDate, LocalDate endDate) {
        log.info("Fetching availability for user: {} from {} to {}", userUri, startDate, endDate);
        
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/user_busy_times")
                        .queryParam("user", userUri)
                        .queryParam("start_time", startDate.format(DateTimeFormatter.ISO_LOCAL_DATE))
                        .queryParam("end_time", endDate.format(DateTimeFormatter.ISO_LOCAL_DATE))
                        .build())
                .retrieve()
                .bodyToMono(CalendlyAvailability.class)
                .doOnSuccess(availability -> log.info("Retrieved availability with {} busy times", 
                        availability.getBusyTimes() != null ? availability.getBusyTimes().size() : 0))
                .doOnError(error -> log.error("Failed to get user availability", error));
    }

    // Scheduled Events Operations
    public Mono<List<CalendlyScheduledEvent>> getScheduledEvents(String userUri, LocalDate startDate, LocalDate endDate) {
        log.info("Fetching scheduled events for user: {} from {} to {}", userUri, startDate, endDate);
        
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path(calendlyProperties.getEndpoints().getScheduledEvents())
                        .queryParam("user", userUri)
                        .queryParam("min_start_time", startDate.atStartOfDay().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                        .queryParam("max_start_time", endDate.atTime(23, 59, 59).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                        .build())
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<CalendlyApiResponse<CalendlyScheduledEvent>>() {})
                .map(CalendlyApiResponse::getCollection)
                .doOnSuccess(events -> log.info("Retrieved {} scheduled events", events.size()))
                .doOnError(error -> log.error("Failed to get scheduled events", error));
    }

    public Mono<CalendlyScheduledEvent> getScheduledEvent(String eventUri) {
        log.info("Fetching scheduled event: {}", eventUri);
        
        return webClient.get()
                .uri(eventUri)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<CalendlyApiResponse<CalendlyScheduledEvent>>() {})
                .map(CalendlyApiResponse::getResource)
                .doOnSuccess(event -> log.info("Retrieved scheduled event: {}", event.getName()))
                .doOnError(error -> log.error("Failed to get scheduled event: {}", eventUri, error));
    }

    public Mono<Void> cancelScheduledEvent(String eventUri, String reason) {
        log.info("Cancelling scheduled event: {} with reason: {}", eventUri, reason);
        
        return webClient.post()
                .uri(eventUri + "/cancellation")
                .bodyValue(new CancelEventRequest(reason))
                .retrieve()
                .bodyToMono(Void.class)
                .doOnSuccess(result -> log.info("Cancelled scheduled event: {}", eventUri))
                .doOnError(error -> log.error("Failed to cancel scheduled event: {}", eventUri, error));
    }

    // Helper class for cancellation request
    private record CancelEventRequest(String reason) {}
}
