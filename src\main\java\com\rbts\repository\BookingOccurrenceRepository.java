package com.rbts.repository;

import com.rbts.entity.Booking;
import com.rbts.entity.BookingOccurrence;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface BookingOccurrenceRepository extends JpaRepository<BookingOccurrence, Long> {

    List<BookingOccurrence> findByBooking(Booking booking);
    
    @Query("SELECT bo FROM BookingOccurrence bo WHERE bo.booking = :booking AND bo.occurrenceTime >= :fromTime")
    List<BookingOccurrence> findUpcomingOccurrencesByBooking(@Param("booking") Booking booking, 
                                                            @Param("fromTime") LocalDateTime fromTime);
    
    @Query("SELECT bo FROM BookingOccurrence bo WHERE bo.occurrenceTime BETWEEN :startTime AND :endTime")
    List<BookingOccurrence> findOccurrencesByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                                       @Param("endTime") LocalDateTime endTime);
}
